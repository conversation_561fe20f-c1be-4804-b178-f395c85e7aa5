//
//  run_tests.m
//  ThreadInterpreterHotfixDemo Test Runner
//
//  Created by AI Assistant on 2025-07-31.
//  Copyright © 2025 Demo. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "HotfixFramework/Tests/TestRunner.h"
#import "HotfixFramework/Tests/HotfixIntegrationTests.h"

void printTestReport(TestReport *report, NSString *title) {
    if (!report) {
        printf("❌ %s - 测试报告为空\n", [title UTF8String]);
        return;
    }
    
    printf("📊 %s 结果:\n", [title UTF8String]);
    printf("   总计: %lu 个测试\n", (unsigned long)report.totalTests);
    printf("   通过: %lu 个测试\n", (unsigned long)report.passedTests);
    printf("   失败: %lu 个测试\n", (unsigned long)report.failedTests);
    printf("   跳过: %lu 个测试\n", (unsigned long)report.skippedTests);
    printf("   成功率: %.1f%%\n", report.totalTests > 0 ? 
          (double)report.passedTests / report.totalTests * 100.0 : 0.0);
    
    if (report.failedTests == 0) {
        printf("   ✅ 所有测试通过！\n");
    } else {
        printf("   ❌ 有测试失败，需要检查\n");
        
        // 显示失败的测试用例
        for (TestCase *testCase in report.testCases) {
            if (testCase.result == TestResultFailed) {
                printf("     - 失败: %s (%s)\n", 
                       [testCase.name UTF8String], 
                       [testCase.testDescription UTF8String]);
            }
        }
    }
    printf("\n");
}

int main(int argc, const char * argv[]) {
    @autoreleasepool {
        printf("🚀 开始完整的热修复框架测试...\n\n");
        
        // 创建测试运行器
        TestRunner *testRunner = [[TestRunner alloc] init];
        
        NSMutableArray<TestReport *> *allReports = [NSMutableArray array];
        NSArray *testTitles = @[@"快速测试", @"组件测试", @"集成测试", @"性能测试", @"稳定性测试"];
        
        // 1. 运行快速测试
        printf("📋 1. 运行快速测试...\n");
        TestReport *quickReport = [testRunner runQuickTests];
        [allReports addObject:quickReport];
        printTestReport(quickReport, @"快速测试");
        
        // 2. 运行组件测试
        printf("🔧 2. 运行组件测试...\n");
        TestReport *componentReport = [testRunner runComponentTests];
        [allReports addObject:componentReport];
        printTestReport(componentReport, @"组件测试");
        
        // 3. 运行集成测试
        printf("🔗 3. 运行集成测试...\n");
        TestReport *integrationReport = [testRunner runIntegrationTests];
        [allReports addObject:integrationReport];
        printTestReport(integrationReport, @"集成测试");
        
        // 4. 运行性能测试
        printf("⚡ 4. 运行性能测试...\n");
        TestReport *performanceReport = [testRunner runPerformanceTests];
        [allReports addObject:performanceReport];
        printTestReport(performanceReport, @"性能测试");
        
        // 5. 运行稳定性测试
        printf("🛡️ 5. 运行稳定性测试...\n");
        TestReport *stabilityReport = [testRunner runStabilityTests];
        [allReports addObject:stabilityReport];
        printTestReport(stabilityReport, @"稳定性测试");
        
        // 生成测试总结
        printf("📈 测试总结报告:\n");
        printf("=====================================\n");
        
        NSUInteger totalTests = 0;
        NSUInteger totalPassed = 0;
        NSUInteger totalFailed = 0;
        NSUInteger totalSkipped = 0;
        
        for (NSInteger i = 0; i < allReports.count && i < testTitles.count; i++) {
            TestReport *report = allReports[i];
            if (report) {
                totalTests += report.totalTests;
                totalPassed += report.passedTests;
                totalFailed += report.failedTests;
                totalSkipped += report.skippedTests;
                
                const char *status = report.failedTests == 0 ? "✅" : "❌";
                printf("%s %s: %lu/%lu 通过\n", status, [testTitles[i] UTF8String],
                      (unsigned long)report.passedTests, (unsigned long)report.totalTests);
            }
        }
        
        printf("=====================================\n");
        printf("🎯 总体统计:\n");
        printf("   总测试数: %lu\n", (unsigned long)totalTests);
        printf("   通过数量: %lu\n", (unsigned long)totalPassed);
        printf("   失败数量: %lu\n", (unsigned long)totalFailed);
        printf("   跳过数量: %lu\n", (unsigned long)totalSkipped);
        printf("   整体成功率: %.1f%%\n", totalTests > 0 ? 
              (double)totalPassed / totalTests * 100.0 : 0.0);
        
        if (totalFailed == 0) {
            printf("🎊 恭喜！所有测试都通过了！\n");
            printf("🚀 热修复框架已准备就绪，可以投入使用！\n");
            return 0;
        } else {
            printf("⚠️  有 %lu 个测试失败，建议检查和修复\n", (unsigned long)totalFailed);
            return 1;
        }
    }
}
