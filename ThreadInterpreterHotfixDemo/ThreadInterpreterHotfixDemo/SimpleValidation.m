//
//  SimpleValidation.m
//  ThreadInterpreterHotfixDemo
//
//  Created by AI Assistant on 2025-07-31.
//  Copyright © 2025 Demo. All rights reserved.
//

#import "SimpleValidation.h"
#import "HotfixFramework/Manager/HotfixManager.h"

@implementation SimpleValidation

+ (NSDictionary *)runSimpleValidation {
    NSLog(@"🚀 开始简化验证测试...");
    NSLog(@"===========================================");
    
    NSMutableDictionary *results = [NSMutableDictionary dictionary];
    NSDate *startTime = [NSDate date];
    
    // 测试1: 验证热修复管理器初始化
    NSLog(@"\n📋 测试1: 验证热修复管理器初始化");
    BOOL test1 = [self validateHotfixManagerInitialization];
    results[@"hotfix_manager_init"] = @(test1);
    NSLog(@"   结果: %@", test1 ? @"✅ 通过" : @"❌ 失败");
    
    // 测试2: 验证基础API调用
    NSLog(@"\n📋 测试2: 验证基础API调用");
    BOOL test2 = [self validateBasicAPICall];
    results[@"basic_api_call"] = @(test2);
    NSLog(@"   结果: %@", test2 ? @"✅ 通过" : @"❌ 失败");
    
    // 测试3: 验证内存管理
    NSLog(@"\n📋 测试3: 验证内存管理");
    BOOL test3 = [self validateMemoryManagement];
    results[@"memory_management"] = @(test3);
    NSLog(@"   结果: %@", test3 ? @"✅ 通过" : @"❌ 失败");
    
    // 计算总体结果
    NSInteger passedTests = 0;
    NSInteger totalTests = 3;
    
    if (test1) passedTests++;
    if (test2) passedTests++;
    if (test3) passedTests++;
    
    NSTimeInterval duration = [[NSDate date] timeIntervalSinceDate:startTime];
    
    results[@"summary"] = @{
        @"total_tests": @(totalTests),
        @"passed_tests": @(passedTests),
        @"failed_tests": @(totalTests - passedTests),
        @"success_rate": @((double)passedTests / totalTests * 100.0),
        @"duration": @(duration),
        @"overall_success": @(passedTests == totalTests)
    };
    
    [self printValidationResults:results];
    
    return [results copy];
}

+ (BOOL)validateHotfixManagerInitialization {
    @try {
        // 1. 获取单例
        HotfixManager *manager = [HotfixManager sharedManager];
        if (!manager) {
            NSLog(@"   ❌ 无法获取HotfixManager单例");
            return NO;
        }
        NSLog(@"   ✅ HotfixManager单例获取成功");
        
        // 2. 初始化
        BOOL initialized = [manager initializeWithMemorySize:1024 * 1024]; // 1MB
        if (!initialized) {
            NSLog(@"   ❌ HotfixManager初始化失败");
            return NO;
        }
        NSLog(@"   ✅ HotfixManager初始化成功");
        
        // 3. 检查核心组件
        if (![manager interpreterCore]) {
            NSLog(@"   ❌ ThreadedInterpreterCore获取失败");
            return NO;
        }
        NSLog(@"   ✅ ThreadedInterpreterCore获取成功");
        
        if (![manager methodRegistry]) {
            NSLog(@"   ❌ MethodRegistry获取失败");
            return NO;
        }
        NSLog(@"   ✅ MethodRegistry获取成功");
        
        return YES;
        
    } @catch (NSException *exception) {
        NSLog(@"   ❌ 初始化验证异常: %@", exception.reason);
        return NO;
    }
}

+ (BOOL)validateBasicAPICall {
    @try {
        HotfixManager *manager = [HotfixManager sharedManager];
        
        // 创建测试目标
        Class testClass = [NSString class];
        SEL testSelector = @selector(length);
        
        // 验证方法存在
        Method originalMethod = class_getInstanceMethod(testClass, testSelector);
        if (!originalMethod) {
            NSLog(@"   ❌ 测试方法不存在");
            return NO;
        }
        NSLog(@"   ✅ 测试方法存在");
        
        // 创建简单的汇编代码
        uint32_t instructions[] = {
            0xD2800020,  // MOV X0, #1
            0xD65F03C0   // RET
        };
        NSData *assemblyCode = [NSData dataWithBytes:instructions length:sizeof(instructions)];
        
        // 尝试调用替换方法API
        HotfixResult result = [manager replaceMethod:testSelector
                                             inClass:testClass
                                        withAssembly:assemblyCode];
        
        NSLog(@"   ✅ replaceMethod API调用成功，返回: %ld", (long)result);
        
        // 尝试调用恢复方法API
        HotfixResult restoreResult = [manager restoreMethod:testSelector inClass:testClass];
        NSLog(@"   ✅ restoreMethod API调用成功，返回: %ld", (long)restoreResult);
        
        return YES;
        
    } @catch (NSException *exception) {
        NSLog(@"   ❌ API调用验证异常: %@", exception.reason);
        return NO;
    }
}

+ (BOOL)validateMemoryManagement {
    @try {
        // 创建多个管理器实例引用，验证单例模式
        HotfixManager *manager1 = [HotfixManager sharedManager];
        HotfixManager *manager2 = [HotfixManager sharedManager];
        
        if (manager1 != manager2) {
            NSLog(@"   ❌ 单例模式失败，创建了多个实例");
            return NO;
        }
        NSLog(@"   ✅ 单例模式正常");
        
        // 验证内存分配
        BOOL initialized = [manager1 initializeWithMemorySize:512 * 1024]; // 512KB
        if (!initialized) {
            NSLog(@"   ❌ 内存分配失败");
            return NO;
        }
        NSLog(@"   ✅ 内存分配成功");
        
        // 验证重复初始化不会造成内存泄漏
        BOOL reinitialized = [manager1 initializeWithMemorySize:1024 * 1024]; // 1MB
        NSLog(@"   ✅ 重复初始化处理正常，返回: %@", reinitialized ? @"YES" : @"NO");
        
        return YES;
        
    } @catch (NSException *exception) {
        NSLog(@"   ❌ 内存管理验证异常: %@", exception.reason);
        return NO;
    }
}

+ (void)printValidationResults:(NSDictionary *)results {
    NSLog(@"\n🎯 简化验证测试总结");
    NSLog(@"===========================================");
    
    NSDictionary *summary = results[@"summary"];
    if (summary) {
        NSLog(@"总计测试: %@ 个", summary[@"total_tests"]);
        NSLog(@"通过测试: %@ 个", summary[@"passed_tests"]);
        NSLog(@"失败测试: %@ 个", summary[@"failed_tests"]);
        NSLog(@"成功率: %.1f%%", [summary[@"success_rate"] doubleValue]);
        NSLog(@"总耗时: %.3f 秒", [summary[@"duration"] doubleValue]);
        
        BOOL overallSuccess = [summary[@"overall_success"] boolValue];
        if (overallSuccess) {
            NSLog(@"\n🎉 恭喜！简化验证测试全部通过！");
            NSLog(@"✅ 热修复框架核心功能正常");
            NSLog(@"✅ 可以进行更深入的测试和应用");
        } else {
            NSLog(@"\n⚠️ 简化验证测试有失败项");
            NSLog(@"❌ 需要检查失败的组件");
        }
    }
    
    NSLog(@"===========================================");
}

@end
