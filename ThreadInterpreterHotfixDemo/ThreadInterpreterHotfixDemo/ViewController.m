//
//  ViewController.m
//  ThreadInterpreterHotfixDemo
//
//  Created by <PERSON> on 2025/7/31.
//

#import "ViewController.h"
#import "HotfixFramework/Manager/HotfixManager.h"
#import "HotfixFramework/Tests/HotfixIntegrationTests.h"
#import "HotfixFramework/Tests/TestRunner.h"

@interface ViewController ()

@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.

    // 延迟执行测试，确保UI完全加载
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self testHotfixFramework];
    });
}

- (void)testHotfixFramework {
    NSLog(@"🚀 开始完整的热修复框架测试...");

    // 创建测试运行器
    TestRunner *testRunner = [[TestRunner alloc] init];

    // 1. 运行快速测试
    NSLog(@"\n📋 1. 运行快速测试...");
    TestReport *quickReport = [testRunner runQuickTests];
    [self printTestReport:quickReport withTitle:@"快速测试"];

    // 2. 运行组件测试
    NSLog(@"\n🔧 2. 运行组件测试...");
    TestReport *componentReport = [testRunner runComponentTests];
    [self printTestReport:componentReport withTitle:@"组件测试"];

    // 3. 运行集成测试
    NSLog(@"\n🔗 3. 运行集成测试...");
    TestReport *integrationReport = [testRunner runIntegrationTests];
    [self printTestReport:integrationReport withTitle:@"集成测试"];

    // 4. 运行性能测试
    NSLog(@"\n⚡ 4. 运行性能测试...");
    TestReport *performanceReport = [testRunner runPerformanceTests];
    [self printTestReport:performanceReport withTitle:@"性能测试"];

    // 5. 运行稳定性测试
    NSLog(@"\n🛡️ 5. 运行稳定性测试...");
    TestReport *stabilityReport = [testRunner runStabilityTests];
    [self printTestReport:stabilityReport withTitle:@"稳定性测试"];

    // 6. 运行完整测试套件
    NSLog(@"\n🎯 6. 运行完整测试套件...");
    TestReport *allReport = [testRunner runAllTests];
    [self printTestReport:allReport withTitle:@"完整测试套件"];

    // 生成测试总结
    [self generateTestSummary:@[quickReport, componentReport, integrationReport,
                               performanceReport, stabilityReport, allReport]];

    NSLog(@"\n🎉 热修复框架测试完成！");
}

- (void)printTestReport:(TestReport *)report withTitle:(NSString *)title {
    if (!report) {
        NSLog(@"❌ %@ - 测试报告为空", title);
        return;
    }

    NSLog(@"📊 %@ 结果:", title);
    NSLog(@"   总计: %lu 个测试", (unsigned long)report.totalTests);
    NSLog(@"   通过: %lu 个测试", (unsigned long)report.passedTests);
    NSLog(@"   失败: %lu 个测试", (unsigned long)report.failedTests);
    NSLog(@"   跳过: %lu 个测试", (unsigned long)report.skippedTests);
    NSLog(@"   成功率: %.1f%%", report.totalTests > 0 ?
          (double)report.passedTests / report.totalTests * 100.0 : 0.0);

    if (report.failedTests == 0) {
        NSLog(@"   ✅ 所有测试通过！");
    } else {
        NSLog(@"   ❌ 有测试失败，需要检查");

        // 显示失败的测试用例
        for (TestCase *testCase in report.testCases) {
            if (testCase.result == TestResultFailed) {
                NSLog(@"     - 失败: %@ (%@)", testCase.name, testCase.testDescription);
            }
        }
    }
}

- (void)generateTestSummary:(NSArray<TestReport *> *)reports {
    NSLog(@"\n📈 测试总结报告:");
    NSLog(@"=====================================");

    NSUInteger totalTests = 0;
    NSUInteger totalPassed = 0;
    NSUInteger totalFailed = 0;
    NSUInteger totalSkipped = 0;

    NSArray *titles = @[@"快速测试", @"组件测试", @"集成测试", @"性能测试", @"稳定性测试", @"完整套件"];

    for (NSInteger i = 0; i < reports.count && i < titles.count; i++) {
        TestReport *report = reports[i];
        if (report) {
            totalTests += report.totalTests;
            totalPassed += report.passedTests;
            totalFailed += report.failedTests;
            totalSkipped += report.skippedTests;

            NSString *status = report.failedTests == 0 ? @"✅" : @"❌";
            NSLog(@"%@ %@: %lu/%lu 通过", status, titles[i],
                  (unsigned long)report.passedTests, (unsigned long)report.totalTests);
        }
    }

    NSLog(@"=====================================");
    NSLog(@"🎯 总体统计:");
    NSLog(@"   总测试数: %lu", (unsigned long)totalTests);
    NSLog(@"   通过数量: %lu", (unsigned long)totalPassed);
    NSLog(@"   失败数量: %lu", (unsigned long)totalFailed);
    NSLog(@"   跳过数量: %lu", (unsigned long)totalSkipped);
    NSLog(@"   整体成功率: %.1f%%", totalTests > 0 ?
          (double)totalPassed / totalTests * 100.0 : 0.0);

    if (totalFailed == 0) {
        NSLog(@"🎊 恭喜！所有测试都通过了！");
        NSLog(@"🚀 热修复框架已准备就绪，可以投入使用！");
    } else {
        NSLog(@"⚠️  有 %lu 个测试失败，建议检查和修复", (unsigned long)totalFailed);
    }
}


@end
