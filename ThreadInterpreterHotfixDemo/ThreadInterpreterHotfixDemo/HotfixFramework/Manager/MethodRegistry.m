//
//  MethodRegistry.m
//  ThreadInterpreterHotfixDemo
//
//  Created by AI Assistant on 2025-07-31.
//  Copyright © 2025 Demo. All rights reserved.
//

#import "MethodRegistry.h"

#pragma mark - MethodRegistryEntry Implementation

@implementation MethodRegistryEntry

+ (instancetype)entryWithClass:(Class)targetClass
                      selector:(SEL)selector
                originalMethod:(Method)originalMethod
                   originalIMP:(IMP)originalIMP
                  assemblyCode:(NSData *)assemblyCode {
    
    MethodRegistryEntry *entry = [[MethodRegistryEntry alloc] init];
    entry.targetClass = targetClass;
    entry.selector = selector;
    entry.originalMethod = originalMethod;
    entry.originalIMP = originalIMP;
    entry.assemblyCode = assemblyCode;
    entry.status = HotfixStatusActive;
    entry.createdAt = [NSDate date];
    entry.lastUsedAt = [NSDate date];
    entry.callCount = 0;
    
    return entry;
}

- (NSString *)methodIdentifier {
    return [NSString stringWithFormat:@"%@.%@", 
            NSStringFromClass(self.targetClass), 
            NSStringFromSelector(self.selector)];
}

- (void)recordCall {
    self.lastUsedAt = [NSDate date];
    self.callCount++;
}

- (NSString *)debugDescription {
    return [NSString stringWithFormat:@"MethodRegistryEntry {\n"
            @"  method: %@\n"
            @"  status: %ld\n"
            @"  callCount: %lu\n"
            @"  createdAt: %@\n"
            @"  lastUsedAt: %@\n"
            @"  assemblySize: %lu bytes\n"
            @"}", 
            [self methodIdentifier],
            (long)self.status,
            (unsigned long)self.callCount,
            self.createdAt,
            self.lastUsedAt,
            (unsigned long)self.assemblyCode.length];
}

@end

#pragma mark - MethodRegistry Implementation

@interface MethodRegistry ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, MethodRegistryEntry *> *registry;
@property (nonatomic, strong) dispatch_queue_t registryQueue;

@end

@implementation MethodRegistry

#pragma mark - 初始化

- (instancetype)init {
    self = [super init];
    if (self) {
        _registry = [[NSMutableDictionary alloc] init];
        _registryQueue = dispatch_queue_create("com.hotfix.methodregistry", DISPATCH_QUEUE_CONCURRENT);
    }
    return self;
}

#pragma mark - 方法注册

- (BOOL)registerMethod:(Class)targetClass
              selector:(SEL)selector
        originalMethod:(Method)originalMethod
           originalIMP:(IMP)originalIMP
             hotfixIMP:(IMP)hotfixIMP
          assemblyCode:(NSData *)assemblyCode {
    
    if (!targetClass || !selector || !originalMethod || !originalIMP || !hotfixIMP || !assemblyCode) {
        return NO;
    }
    
    NSString *methodKey = [self methodKeyForClass:targetClass selector:selector];
    
    __block BOOL success = NO;
    dispatch_barrier_sync(_registryQueue, ^{
        // 检查是否已存在
        if (self.registry[methodKey]) {
            // 方法已经注册，这是一个错误状态
            // 应该先调用unregisterMethod或restoreMethod
            return;
        }

        // 创建注册表项
        MethodRegistryEntry *entry = [MethodRegistryEntry entryWithClass:targetClass
                                                                 selector:selector
                                                           originalMethod:originalMethod
                                                              originalIMP:originalIMP
                                                             assemblyCode:assemblyCode];
        entry.hotfixIMP = hotfixIMP;

        // 添加到注册表
        self.registry[methodKey] = entry;
        success = YES;
    });
    
    return success;
}

- (BOOL)unregisterMethod:(Class)targetClass selector:(SEL)selector {
    if (!targetClass || !selector) {
        return NO;
    }
    
    NSString *methodKey = [self methodKeyForClass:targetClass selector:selector];
    
    __block BOOL success = NO;
    dispatch_barrier_sync(_registryQueue, ^{
        if (self.registry[methodKey]) {
            [self.registry removeObjectForKey:methodKey];
            success = YES;
        }
    });
    
    return success;
}

- (NSUInteger)unregisterAllMethods {
    __block NSUInteger count = 0;
    dispatch_barrier_sync(_registryQueue, ^{
        count = self.registry.count;
        [self.registry removeAllObjects];
    });
    
    return count;
}

#pragma mark - 方法查询

- (BOOL)isMethodRegistered:(Class)targetClass selector:(SEL)selector {
    if (!targetClass || !selector) {
        return NO;
    }
    
    NSString *methodKey = [self methodKeyForClass:targetClass selector:selector];
    
    __block BOOL exists = NO;
    dispatch_sync(_registryQueue, ^{
        exists = (self.registry[methodKey] != nil);
    });
    
    return exists;
}

- (nullable MethodRegistryEntry *)getMethodEntry:(Class)targetClass selector:(SEL)selector {
    if (!targetClass || !selector) {
        return nil;
    }
    
    NSString *methodKey = [self methodKeyForClass:targetClass selector:selector];
    
    __block MethodRegistryEntry *entry = nil;
    dispatch_sync(_registryQueue, ^{
        entry = self.registry[methodKey];
    });
    
    return entry;
}

- (NSArray<MethodRegistryEntry *> *)getAllMethods {
    __block NSArray<MethodRegistryEntry *> *allMethods = nil;
    dispatch_sync(_registryQueue, ^{
        allMethods = [self.registry.allValues copy];
    });
    
    return allMethods ?: @[];
}

- (NSArray<MethodRegistryEntry *> *)getMethodsForClass:(Class)targetClass {
    if (!targetClass) {
        return @[];
    }
    
    __block NSMutableArray<MethodRegistryEntry *> *classMethods = [[NSMutableArray alloc] init];
    dispatch_sync(_registryQueue, ^{
        for (MethodRegistryEntry *entry in self.registry.allValues) {
            if (entry.targetClass == targetClass) {
                [classMethods addObject:entry];
            }
        }
    });
    
    return [classMethods copy];
}

- (NSArray<MethodRegistryEntry *> *)getMethodsWithStatus:(HotfixStatus)status {
    __block NSMutableArray<MethodRegistryEntry *> *statusMethods = [[NSMutableArray alloc] init];
    dispatch_sync(_registryQueue, ^{
        for (MethodRegistryEntry *entry in self.registry.allValues) {
            if (entry.status == status) {
                [statusMethods addObject:entry];
            }
        }
    });
    
    return [statusMethods copy];
}

#pragma mark - 统计信息

- (NSUInteger)totalMethodCount {
    __block NSUInteger count = 0;
    dispatch_sync(_registryQueue, ^{
        count = self.registry.count;
    });
    
    return count;
}

- (NSUInteger)activeMethodCount {
    __block NSUInteger count = 0;
    dispatch_sync(_registryQueue, ^{
        for (MethodRegistryEntry *entry in self.registry.allValues) {
            if (entry.status == HotfixStatusActive) {
                count++;
            }
        }
    });
    
    return count;
}

- (NSUInteger)totalCallCount {
    __block NSUInteger totalCalls = 0;
    dispatch_sync(_registryQueue, ^{
        for (MethodRegistryEntry *entry in self.registry.allValues) {
            totalCalls += entry.callCount;
        }
    });
    
    return totalCalls;
}

- (void)resetStatistics {
    dispatch_barrier_sync(_registryQueue, ^{
        for (MethodRegistryEntry *entry in self.registry.allValues) {
            entry.callCount = 0;
            entry.lastUsedAt = [NSDate date];
        }
    });
}

#pragma mark - 方法状态管理

- (BOOL)updateMethodStatus:(Class)targetClass selector:(SEL)selector status:(HotfixStatus)status {
    MethodRegistryEntry *entry = [self getMethodEntry:targetClass selector:selector];
    if (!entry) {
        return NO;
    }
    
    dispatch_barrier_sync(_registryQueue, ^{
        entry.status = status;
    });
    
    return YES;
}

- (void)recordMethodCall:(Class)targetClass selector:(SEL)selector {
    MethodRegistryEntry *entry = [self getMethodEntry:targetClass selector:selector];
    if (entry) {
        dispatch_barrier_sync(_registryQueue, ^{
            [entry recordCall];
        });
    }
}

#pragma mark - 调试支持

- (NSString *)debugDescription {
    __block NSMutableString *description = [[NSMutableString alloc] init];
    
    dispatch_sync(_registryQueue, ^{
        [description appendFormat:@"MethodRegistry Debug Info:\n"];
        [description appendFormat:@"- Total Methods: %lu\n", (unsigned long)self.registry.count];
        [description appendFormat:@"- Active Methods: %lu\n", (unsigned long)[self activeMethodCount]];
        [description appendFormat:@"- Total Calls: %lu\n", (unsigned long)[self totalCallCount]];
        
        [description appendString:@"\nRegistered Methods:\n"];
        for (NSString *key in [self.registry.allKeys sortedArrayUsingSelector:@selector(compare:)]) {
            MethodRegistryEntry *entry = self.registry[key];
            [description appendFormat:@"  %@: %lu calls, status=%ld\n", 
             key, (unsigned long)entry.callCount, (long)entry.status];
        }
    });
    
    return [description copy];
}

- (void)printAllMethods {
    NSLog(@"%@", [self debugDescription]);
}

- (NSDictionary *)exportRegistryData {
    __block NSMutableDictionary *exportData = [[NSMutableDictionary alloc] init];
    
    dispatch_sync(_registryQueue, ^{
        exportData[@"totalMethods"] = @(self.registry.count);
        exportData[@"activeMethods"] = @([self activeMethodCount]);
        exportData[@"totalCalls"] = @([self totalCallCount]);
        exportData[@"exportTime"] = [NSDate date];
        
        NSMutableArray *methodsData = [[NSMutableArray alloc] init];
        for (MethodRegistryEntry *entry in self.registry.allValues) {
            NSDictionary *methodData = @{
                @"class": NSStringFromClass(entry.targetClass),
                @"selector": NSStringFromSelector(entry.selector),
                @"status": @(entry.status),
                @"callCount": @(entry.callCount),
                @"createdAt": entry.createdAt,
                @"lastUsedAt": entry.lastUsedAt,
                @"assemblySize": @(entry.assemblyCode.length)
            };
            [methodsData addObject:methodData];
        }
        exportData[@"methods"] = methodsData;
    });
    
    return [exportData copy];
}

#pragma mark - 私有方法

- (NSString *)methodKeyForClass:(Class)targetClass selector:(SEL)selector {
    return [NSString stringWithFormat:@"%@.%@", 
            NSStringFromClass(targetClass), 
            NSStringFromSelector(selector)];
}

@end
