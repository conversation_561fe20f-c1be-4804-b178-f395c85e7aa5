//
//  HotfixManager.h
//  ThreadInterpreterHotfixDemo
//
//  Created by AI Assistant on 2025-07-31.
//  Copyright © 2025 Demo. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <objc/runtime.h>

NS_ASSUME_NONNULL_BEGIN

// 前向声明
@class ThreadedInterpreterCore;
@class MethodRegistry;

/**
 * 热修复结果枚举
 */
typedef NS_ENUM(NSInteger, HotfixResult) {
    HotfixResultSuccess = 0,           // 成功
    HotfixResultInvalidMethod,         // 无效方法
    HotfixResultInvalidAssembly,       // 无效汇编代码
    HotfixResultExecutionError,        // 执行错误
    HotfixResultMemoryError,           // 内存错误
    HotfixResultAlreadyHooked,         // 方法已被Hook
    HotfixResultNotHooked,             // 方法未被Hook
    HotfixResultNotInitialized,        // 未初始化
    HotfixResultValidationFailed,      // 代码验证失败
    HotfixResultRegistrationFailed,    // 注册失败
    HotfixResultConcurrencyError,      // 并发错误
    HotfixResultUnknownError           // 未知错误
};

/**
 * 热修复错误信息
 */
@interface HotfixError : NSObject

@property (nonatomic, assign, readonly) HotfixResult result;
@property (nonatomic, strong, readonly) NSString *errorMessage;
@property (nonatomic, strong, readonly) NSString *suggestion;
@property (nonatomic, strong, readonly) NSDictionary *userInfo;
@property (nonatomic, strong, readonly) NSDate *timestamp;

+ (instancetype)errorWithResult:(HotfixResult)result
                        message:(NSString *)message
                     suggestion:(NSString *)suggestion
                       userInfo:(NSDictionary *)userInfo;

+ (NSString *)descriptionForResult:(HotfixResult)result;

@end

/**
 * 热修复状态枚举
 */
typedef NS_ENUM(NSInteger, HotfixStatus) {
    HotfixStatusInactive = 0,          // 未激活
    HotfixStatusActive,                // 已激活
    HotfixStatusError                  // 错误状态
};

/**
 * 方法Hook信息结构
 */
typedef struct HotfixMethodInfo {
    Class targetClass;                 // 目标类
    SEL selector;                      // 方法选择器
    Method originalMethod;             // 原始方法
    IMP originalIMP;                   // 原始实现
    IMP hotfixIMP;                     // 热修复实现
    NSData *assemblyCode;              // 汇编代码
    HotfixStatus status;               // 状态
    NSDate *createdAt;                 // 创建时间
    NSDate *lastUsedAt;                // 最后使用时间
    NSUInteger callCount;              // 调用次数
} HotfixMethodInfo;

/**
 * HotfixManager - 热修复管理器
 * 
 * 核心功能：
 * 1. 管理方法的热修复替换
 * 2. 集成ThreadedInterpreterCore进行汇编代码执行
 * 3. 提供方法Hook和恢复机制
 * 4. 维护热修复方法的注册表
 */
@interface HotfixManager : NSObject

#pragma mark - 单例管理

/**
 * 获取共享实例
 * @return 热修复管理器单例
 */
+ (instancetype)sharedManager;

#pragma mark - 初始化配置

/**
 * 初始化热修复管理器
 * @param memorySize 解释器虚拟内存大小
 * @return 是否初始化成功
 */
- (BOOL)initializeWithMemorySize:(size_t)memorySize;

/**
 * 获取解释器核心实例
 * @return 解释器核心
 */
- (ThreadedInterpreterCore *)interpreterCore;

/**
 * 获取方法注册表
 * @return 方法注册表
 */
- (MethodRegistry *)methodRegistry;

#pragma mark - 方法替换

/**
 * 使用汇编代码替换方法
 * @param selector 方法选择器
 * @param targetClass 目标类
 * @param assemblyCode 汇编代码数据
 * @return 热修复结果
 */
- (HotfixResult)replaceMethod:(SEL)selector
                      inClass:(Class)targetClass
                 withAssembly:(NSData *)assemblyCode;

/**
 * 使用汇编代码替换方法（增强版本，返回详细错误信息）
 * @param selector 方法选择器
 * @param targetClass 目标类
 * @param assemblyCode 汇编代码数据
 * @param error 输出参数，包含详细错误信息
 * @return 热修复结果
 */
- (HotfixResult)replaceMethod:(SEL)selector
                      inClass:(Class)targetClass
                 withAssembly:(NSData *)assemblyCode
                        error:(HotfixError **)error;

/**
 * 使用汇编字符串替换方法
 * @param selector 方法选择器
 * @param targetClass 目标类
 * @param assemblyString 汇编代码字符串
 * @return 热修复结果
 */
- (HotfixResult)replaceMethod:(SEL)selector 
                      inClass:(Class)targetClass 
            withAssemblyString:(NSString *)assemblyString;

/**
 * 恢复方法的原始实现
 * @param selector 方法选择器
 * @param targetClass 目标类
 * @return 热修复结果
 */
- (HotfixResult)restoreMethod:(SEL)selector inClass:(Class)targetClass;

/**
 * 恢复所有被Hook的方法
 * @return 恢复成功的方法数量
 */
- (NSUInteger)restoreAllMethods;

#pragma mark - 方法查询

/**
 * 检查方法是否已被Hook
 * @param selector 方法选择器
 * @param targetClass 目标类
 * @return 是否已被Hook
 */
- (BOOL)isMethodHooked:(SEL)selector inClass:(Class)targetClass;

/**
 * 获取方法的Hook信息
 * @param selector 方法选择器
 * @param targetClass 目标类
 * @return Hook信息，未Hook返回NULL
 */
- (nullable HotfixMethodInfo *)getMethodInfo:(SEL)selector inClass:(Class)targetClass;

/**
 * 获取所有被Hook的方法信息
 * @return 方法信息数组
 */
- (NSArray<NSValue *> *)getAllHookedMethods;

#pragma mark - 统计信息

/**
 * 获取热修复统计信息
 * @param totalMethods 总Hook方法数
 * @param activeMethods 活跃方法数
 * @param totalCalls 总调用次数
 */
- (void)getStatistics:(NSUInteger *)totalMethods 
        activeMethods:(NSUInteger *)activeMethods 
           totalCalls:(NSUInteger *)totalCalls;

/**
 * 重置统计信息
 */
- (void)resetStatistics;

#pragma mark - 调试支持

/**
 * 启用/禁用调试模式
 * @param enabled 是否启用
 */
- (void)setDebugEnabled:(BOOL)enabled;

/**
 * 获取调试信息
 * @return 调试信息字符串
 */
- (NSString *)debugDescription;

/**
 * 打印所有Hook方法的信息
 */
- (void)printAllHookedMethods;

#pragma mark - 错误处理和诊断

/**
 * 获取最后一个错误信息
 * @return 最后一个错误信息，如果没有错误则返回nil
 */
- (HotfixError *)lastError;

/**
 * 清除错误历史
 */
- (void)clearErrorHistory;

/**
 * 获取错误历史
 * @return 错误历史数组
 */
- (NSArray<HotfixError *> *)errorHistory;

/**
 * 执行系统诊断
 * @return 诊断报告
 */
- (NSDictionary *)performSystemDiagnostics;

/**
 * 尝试自动恢复错误状态
 * @return 是否成功恢复
 */
- (BOOL)attemptErrorRecovery;

/**
 * 验证系统完整性
 * @return 验证结果
 */
- (BOOL)validateSystemIntegrity;

@end

NS_ASSUME_NONNULL_END
