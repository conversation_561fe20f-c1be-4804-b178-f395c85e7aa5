//
//  HotfixManager.m
//  ThreadInterpreterHotfixDemo
//
//  Created by AI Assistant on 2025-07-31.
//  Copyright © 2025 Demo. All rights reserved.
//

#import "HotfixManager.h"
#import "MethodRegistry.h"
#import "../../TCTI/ThreadedInterpreterCore.h"
#import "../../TCTI/VirtualCPUState.h"
#import <objc/message.h>

#pragma mark - HotfixError Implementation

@implementation HotfixError

+ (instancetype)errorWithResult:(HotfixResult)result
                        message:(NSString *)message
                     suggestion:(NSString *)suggestion
                       userInfo:(NSDictionary *)userInfo {
    HotfixError *error = [[HotfixError alloc] init];
    error->_result = result;
    error->_errorMessage = message ?: [self descriptionForResult:result];
    error->_suggestion = suggestion ?: [self suggestionForResult:result];
    error->_userInfo = userInfo ?: @{};
    error->_timestamp = [NSDate date];
    return error;
}

+ (NSString *)descriptionForResult:(HotfixResult)result {
    switch (result) {
        case HotfixResultSuccess:
            return @"操作成功完成";
        case HotfixResultInvalidMethod:
            return @"无效的方法或参数";
        case HotfixResultInvalidAssembly:
            return @"无效的汇编代码";
        case HotfixResultExecutionError:
            return @"汇编代码执行失败";
        case HotfixResultMemoryError:
            return @"内存分配或访问错误";
        case HotfixResultAlreadyHooked:
            return @"方法已经被Hook，无法重复Hook";
        case HotfixResultNotHooked:
            return @"方法未被Hook，无法恢复";
        case HotfixResultNotInitialized:
            return @"热修复管理器未初始化";
        case HotfixResultValidationFailed:
            return @"代码验证失败";
        case HotfixResultRegistrationFailed:
            return @"方法注册失败";
        case HotfixResultConcurrencyError:
            return @"并发操作冲突";
        case HotfixResultUnknownError:
        default:
            return @"未知错误";
    }
}

+ (NSString *)suggestionForResult:(HotfixResult)result {
    switch (result) {
        case HotfixResultInvalidMethod:
            return @"请检查方法选择器和目标类是否正确，确保方法存在";
        case HotfixResultInvalidAssembly:
            return @"请检查汇编代码格式和语法，确保代码有效";
        case HotfixResultExecutionError:
            return @"请检查汇编代码逻辑，确保寄存器使用正确";
        case HotfixResultMemoryError:
            return @"请检查内存使用情况，可能需要增加内存分配";
        case HotfixResultAlreadyHooked:
            return @"请先恢复原方法，然后再进行新的Hook操作";
        case HotfixResultNotHooked:
            return @"请确认方法已被Hook，或检查方法签名是否匹配";
        case HotfixResultNotInitialized:
            return @"请先调用initializeWithMemorySize:方法初始化管理器";
        case HotfixResultValidationFailed:
            return @"请检查代码安全性，确保符合验证规则";
        case HotfixResultRegistrationFailed:
            return @"请检查系统状态，可能需要重启管理器";
        case HotfixResultConcurrencyError:
            return @"请避免并发操作同一方法，或使用适当的同步机制";
        default:
            return @"请查看详细日志信息，或联系技术支持";
    }
}

- (NSString *)description {
    return [NSString stringWithFormat:@"HotfixError: %@ - %@ (建议: %@)",
            [HotfixError descriptionForResult:self.result],
            self.errorMessage,
            self.suggestion];
}

@end

#pragma mark - HotfixManager Implementation

@interface HotfixManager ()

@property (nonatomic, strong) ThreadedInterpreterCore *interpreterCore;
@property (nonatomic, strong) MethodRegistry *methodRegistry;
@property (nonatomic, assign) BOOL debugEnabled;
@property (nonatomic, assign) BOOL initialized;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSValue *> *hookedMethods;

// 并发安全控制
@property (nonatomic, strong) dispatch_queue_t hotfixQueue;
@property (nonatomic, strong) NSRecursiveLock *operationLock;

// 错误处理
@property (nonatomic, strong) NSMutableArray<HotfixError *> *errorHistory;
@property (nonatomic, strong) HotfixError *lastError;

@end

@implementation HotfixManager

#pragma mark - 单例管理

+ (instancetype)sharedManager {
    static HotfixManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[HotfixManager alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _methodRegistry = [[MethodRegistry alloc] init];
        _debugEnabled = NO;
        _initialized = NO;
        _hookedMethods = [[NSMutableDictionary alloc] init];

        // 初始化并发安全控制
        _hotfixQueue = dispatch_queue_create("com.hotfix.manager", DISPATCH_QUEUE_SERIAL);
        _operationLock = [[NSRecursiveLock alloc] init];
        _operationLock.name = @"HotfixManagerOperationLock";

        // 初始化错误处理
        _errorHistory = [[NSMutableArray alloc] init];
        _lastError = nil;
    }
    return self;
}

#pragma mark - 初始化配置

- (BOOL)initializeWithMemorySize:(size_t)memorySize {
    if (_initialized) {
        NSLog(@"[HotfixManager] Already initialized");
        return YES;
    }
    
    // 创建解释器核心
    _interpreterCore = [[ThreadedInterpreterCore alloc] initWithMemorySize:memorySize];
    if (!_interpreterCore) {
        NSLog(@"[HotfixManager] Failed to create interpreter core");
        return NO;
    }
    
    // 启用调试模式
    _interpreterCore.debugEnabled = _debugEnabled;
    
    _initialized = YES;
    
    if (_debugEnabled) {
        NSLog(@"[HotfixManager] Initialized with memory size: %zu bytes", memorySize);
    }
    
    return YES;
}

- (ThreadedInterpreterCore *)interpreterCore {
    return _interpreterCore;
}

- (MethodRegistry *)methodRegistry {
    return _methodRegistry;
}

#pragma mark - 方法替换

- (HotfixResult)replaceMethod:(SEL)selector
                      inClass:(Class)targetClass
                 withAssembly:(NSData *)assemblyCode {

    // 使用递归锁保护整个操作
    [_operationLock lock];
    @try {
        if (!_initialized) {
            [self recordError:HotfixResultNotInitialized
                      message:@"热修复管理器未初始化"
                   suggestion:@"请先调用initializeWithMemorySize:方法"
                     userInfo:nil];
            return HotfixResultNotInitialized;
        }

        if (!selector || !targetClass || !assemblyCode || assemblyCode.length == 0) {
            [self recordError:HotfixResultInvalidMethod
                      message:@"无效的方法参数"
                   suggestion:@"请检查方法选择器、目标类和汇编代码是否有效"
                     userInfo:@{
                         @"selector": selector ? NSStringFromSelector(selector) : @"nil",
                         @"targetClass": targetClass ? NSStringFromClass(targetClass) : @"nil",
                         @"assemblyCodeLength": @(assemblyCode.length)
                     }];
            return HotfixResultInvalidMethod;
        }

        // 检查方法是否已被Hook
        if ([_methodRegistry isMethodRegistered:targetClass selector:selector]) {
            [self recordError:HotfixResultAlreadyHooked
                      message:[NSString stringWithFormat:@"方法 %@.%@ 已被Hook",
                              NSStringFromClass(targetClass), NSStringFromSelector(selector)]
                   suggestion:@"请先恢复原方法，然后再进行新的Hook操作"
                     userInfo:@{
                         @"class": NSStringFromClass(targetClass),
                         @"selector": NSStringFromSelector(selector)
                     }];
            return HotfixResultAlreadyHooked;
        }
    
    // 获取原始方法
    Method originalMethod = class_getInstanceMethod(targetClass, selector);
    if (!originalMethod) {
        [self recordError:HotfixResultInvalidMethod
                  message:[NSString stringWithFormat:@"在类 %@ 中未找到方法 %@",
                          NSStringFromClass(targetClass), NSStringFromSelector(selector)]
               suggestion:@"请检查方法名称和类是否正确，确保方法存在"
                 userInfo:@{
                     @"class": NSStringFromClass(targetClass),
                     @"selector": NSStringFromSelector(selector),
                     @"methodType": @"instance"
                 }];
        return HotfixResultInvalidMethod;
    }

    // 保存原始实现
    IMP originalIMP = method_getImplementation(originalMethod);

    // 创建热修复实现
    IMP hotfixIMP = [self createHotfixIMPForMethod:originalMethod
                                      assemblyCode:assemblyCode
                                       targetClass:targetClass
                                          selector:selector];

    if (!hotfixIMP) {
        [self recordError:HotfixResultExecutionError
                  message:@"创建热修复实现失败"
               suggestion:@"请检查汇编代码格式和解释器状态"
                 userInfo:@{
                     @"class": NSStringFromClass(targetClass),
                     @"selector": NSStringFromSelector(selector),
                     @"assemblyCodeLength": @(assemblyCode.length)
                 }];
        return HotfixResultExecutionError;
    }
    
    // 替换方法实现
    method_setImplementation(originalMethod, hotfixIMP);
    
    // 注册到方法注册表
    BOOL registered = [_methodRegistry registerMethod:targetClass
                                              selector:selector
                                        originalMethod:originalMethod
                                           originalIMP:originalIMP
                                             hotfixIMP:hotfixIMP
                                          assemblyCode:assemblyCode];
    
    if (!registered) {
        // 恢复原始实现
        method_setImplementation(originalMethod, originalIMP);
        [self recordError:HotfixResultRegistrationFailed
                  message:[NSString stringWithFormat:@"方法 %@.%@ 注册失败",
                          NSStringFromClass(targetClass), NSStringFromSelector(selector)]
               suggestion:@"请检查方法注册表状态，可能需要重启管理器"
                 userInfo:@{
                     @"class": NSStringFromClass(targetClass),
                     @"selector": NSStringFromSelector(selector),
                     @"registryState": [_methodRegistry debugDescription] ?: @"unknown"
                 }];
        return HotfixResultRegistrationFailed;
    }
    
        if (_debugEnabled) {
            NSLog(@"[HotfixManager] Successfully replaced method: %@.%@",
                  NSStringFromClass(targetClass), NSStringFromSelector(selector));
        }

        return HotfixResultSuccess;

    } @finally {
        [_operationLock unlock];
    }
}

- (HotfixResult)replaceMethod:(SEL)selector 
                      inClass:(Class)targetClass 
            withAssemblyString:(NSString *)assemblyString {
    
    if (!assemblyString || assemblyString.length == 0) {
        return HotfixResultInvalidAssembly;
    }
    
    // 将汇编字符串转换为数据
    NSData *assemblyData = [assemblyString dataUsingEncoding:NSUTF8StringEncoding];
    
    return [self replaceMethod:selector inClass:targetClass withAssembly:assemblyData];
}

- (HotfixResult)replaceMethod:(SEL)selector
                      inClass:(Class)targetClass
                 withAssembly:(NSData *)assemblyCode
                        error:(HotfixError **)error {

    HotfixResult result = [self replaceMethod:selector inClass:targetClass withAssembly:assemblyCode];

    if (error) {
        *error = (result == HotfixResultSuccess) ? nil : _lastError;
    }

    return result;
}

- (HotfixResult)restoreMethod:(SEL)selector inClass:(Class)targetClass {
    // 使用递归锁保护整个操作
    [_operationLock lock];
    @try {
        if (!_initialized) {
            return HotfixResultUnknownError;
        }

        if (!selector || !targetClass) {
            return HotfixResultInvalidMethod;
        }

        // 获取注册信息
        MethodRegistryEntry *entry = [_methodRegistry getMethodEntry:targetClass selector:selector];
        if (!entry) {
            return HotfixResultNotHooked;
        }

        // 恢复原始实现
        method_setImplementation(entry.originalMethod, entry.originalIMP);

        // 从注册表中移除
        [_methodRegistry unregisterMethod:targetClass selector:selector];

        if (_debugEnabled) {
            NSLog(@"[HotfixManager] Successfully restored method: %@.%@",
                  NSStringFromClass(targetClass), NSStringFromSelector(selector));
        }

        return HotfixResultSuccess;

    } @finally {
        [_operationLock unlock];
    }
}

- (NSUInteger)restoreAllMethods {
    // 使用递归锁保护整个操作
    [_operationLock lock];
    @try {
        NSArray<MethodRegistryEntry *> *allMethods = [_methodRegistry getAllMethods];
        NSUInteger restoredCount = 0;

        for (MethodRegistryEntry *entry in allMethods) {
            // restoreMethod内部也会获取锁，但使用递归锁所以不会死锁
            HotfixResult result = [self restoreMethod:entry.selector inClass:entry.targetClass];
            if (result == HotfixResultSuccess) {
                restoredCount++;
            }
        }

        if (_debugEnabled) {
            NSLog(@"[HotfixManager] Restored %lu methods", (unsigned long)restoredCount);
        }

        return restoredCount;

    } @finally {
        [_operationLock unlock];
    }
}

#pragma mark - 方法查询

- (BOOL)isMethodHooked:(SEL)selector inClass:(Class)targetClass {
    return [_methodRegistry isMethodRegistered:targetClass selector:selector];
}

- (nullable HotfixMethodInfo *)getMethodInfo:(SEL)selector inClass:(Class)targetClass {
    MethodRegistryEntry *entry = [_methodRegistry getMethodEntry:targetClass selector:selector];
    if (!entry) {
        return NULL;
    }
    
    // 创建HotfixMethodInfo结构
    HotfixMethodInfo *info = malloc(sizeof(HotfixMethodInfo));
    if (!info) {
        return NULL;
    }
    
    info->targetClass = entry.targetClass;
    info->selector = entry.selector;
    info->originalMethod = entry.originalMethod;
    info->originalIMP = entry.originalIMP;
    info->hotfixIMP = entry.hotfixIMP;
    info->assemblyCode = entry.assemblyCode;
    info->status = entry.status;
    info->createdAt = entry.createdAt;
    info->lastUsedAt = entry.lastUsedAt;
    info->callCount = entry.callCount;
    
    return info;
}

- (NSArray<NSValue *> *)getAllHookedMethods {
    NSArray<MethodRegistryEntry *> *allMethods = [_methodRegistry getAllMethods];
    NSMutableArray<NSValue *> *result = [[NSMutableArray alloc] initWithCapacity:allMethods.count];
    
    for (MethodRegistryEntry *entry in allMethods) {
        HotfixMethodInfo *info = [self getMethodInfo:entry.selector inClass:entry.targetClass];
        if (info) {
            NSValue *value = [NSValue valueWithPointer:info];
            [result addObject:value];
        }
    }
    
    return [result copy];
}

#pragma mark - 统计信息

- (void)getStatistics:(NSUInteger *)totalMethods 
        activeMethods:(NSUInteger *)activeMethods 
           totalCalls:(NSUInteger *)totalCalls {
    
    if (totalMethods) {
        *totalMethods = [_methodRegistry totalMethodCount];
    }
    
    if (activeMethods) {
        *activeMethods = [_methodRegistry activeMethodCount];
    }
    
    if (totalCalls) {
        *totalCalls = [_methodRegistry totalCallCount];
    }
}

- (void)resetStatistics {
    [_methodRegistry resetStatistics];
}

#pragma mark - 调试支持

- (void)setDebugEnabled:(BOOL)enabled {
    _debugEnabled = enabled;
    if (_interpreterCore) {
        _interpreterCore.debugEnabled = enabled;
    }
}

- (NSString *)debugDescription {
    NSMutableString *description = [[NSMutableString alloc] init];
    
    [description appendFormat:@"HotfixManager Debug Info:\n"];
    [description appendFormat:@"- Initialized: %@\n", _initialized ? @"YES" : @"NO"];
    [description appendFormat:@"- Debug Enabled: %@\n", _debugEnabled ? @"YES" : @"NO"];
    
    NSUInteger totalMethods, activeMethods, totalCalls;
    [self getStatistics:&totalMethods activeMethods:&activeMethods totalCalls:&totalCalls];
    
    [description appendFormat:@"- Total Methods: %lu\n", (unsigned long)totalMethods];
    [description appendFormat:@"- Active Methods: %lu\n", (unsigned long)activeMethods];
    [description appendFormat:@"- Total Calls: %lu\n", (unsigned long)totalCalls];
    
    if (_interpreterCore) {
        [description appendFormat:@"- Interpreter: %@\n", _interpreterCore.debugDescription];
    }
    
    return [description copy];
}

- (void)printAllHookedMethods {
    [_methodRegistry printAllMethods];
}

#pragma mark - 私有方法

/**
 * 为方法创建热修复IMP
 */
- (IMP)createHotfixIMPForMethod:(Method)method 
                   assemblyCode:(NSData *)assemblyCode 
                    targetClass:(Class)targetClass 
                       selector:(SEL)selector {
    
    // 这里需要创建一个能够执行汇编代码的IMP
    // 暂时返回一个简单的实现，后续会在RuntimeBridge中完善
    
    IMP hotfixIMP = imp_implementationWithBlock(^id(id self, ...) {
        
        if (_debugEnabled) {
            NSLog(@"[HotfixManager] Executing hotfix for %@.%@", 
                  NSStringFromClass(targetClass), NSStringFromSelector(selector));
        }
        
        // 记录方法调用
        [_methodRegistry recordMethodCall:targetClass selector:selector];
        
        // 执行汇编代码
        BOOL success = [_interpreterCore executeAssemblyData:assemblyCode];
        
        if (!success) {
            NSLog(@"[HotfixManager] Failed to execute assembly code for %@.%@", 
                  NSStringFromClass(targetClass), NSStringFromSelector(selector));
            return nil;
        }
        
        // 获取返回值 (简化实现，后续在RuntimeBridge中完善)
        uint64_t returnValue = [_interpreterCore getGeneralRegister:0]; // X0寄存器通常存储返回值
        
        return @(returnValue);
    });
    
    return hotfixIMP;
}

#pragma mark - 错误处理私有方法

- (void)recordError:(HotfixResult)result
            message:(NSString *)message
         suggestion:(NSString *)suggestion
           userInfo:(NSDictionary *)userInfo {

    HotfixError *error = [HotfixError errorWithResult:result
                                              message:message
                                           suggestion:suggestion
                                             userInfo:userInfo];

    // 记录到历史
    [_errorHistory addObject:error];

    // 限制历史记录数量（保留最近100条）
    if (_errorHistory.count > 100) {
        [_errorHistory removeObjectAtIndex:0];
    }

    // 更新最后错误
    _lastError = error;

    // 如果启用调试，输出详细错误信息
    if (_debugEnabled) {
        NSLog(@"[HotfixManager] Error recorded: %@", error);
    }
}

#pragma mark - 错误处理和诊断公共方法

- (HotfixError *)lastError {
    return _lastError;
}

- (void)clearErrorHistory {
    [_errorHistory removeAllObjects];
    _lastError = nil;

    if (_debugEnabled) {
        NSLog(@"[HotfixManager] Error history cleared");
    }
}

- (NSArray<HotfixError *> *)errorHistory {
    return [_errorHistory copy];
}

- (NSDictionary *)performSystemDiagnostics {
    NSMutableDictionary *diagnostics = [[NSMutableDictionary alloc] init];

    // 基本状态信息
    diagnostics[@"initialized"] = @(_initialized);
    diagnostics[@"debugEnabled"] = @(_debugEnabled);
    diagnostics[@"timestamp"] = [NSDate date];

    // 组件状态
    diagnostics[@"interpreterCore"] = _interpreterCore ? @"Available" : @"Not Available";
    diagnostics[@"methodRegistry"] = _methodRegistry ? @"Available" : @"Not Available";

    // 错误统计
    diagnostics[@"errorCount"] = @(_errorHistory.count);
    diagnostics[@"lastErrorTime"] = _lastError ? _lastError.timestamp : [NSNull null];

    // 方法统计
    if (_methodRegistry) {
        NSArray *allMethods = [_methodRegistry getAllMethods];
        diagnostics[@"hookedMethodCount"] = @(allMethods.count);

        // 统计各种状态的方法数量
        NSInteger activeCount = 0;
        NSInteger errorCount = 0;
        for (MethodRegistryEntry *entry in allMethods) {
            // 这里可以根据实际的状态字段进行统计
            activeCount++;
        }
        diagnostics[@"activeMethodCount"] = @(activeCount);
        diagnostics[@"errorMethodCount"] = @(errorCount);
    }

    // 内存使用情况
    if (_interpreterCore) {
        // 这里可以添加内存使用统计
        diagnostics[@"memoryStatus"] = @"Available";
    }

    // 并发控制状态
    diagnostics[@"concurrencyControl"] = @{
        @"hotfixQueue": _hotfixQueue ? @"Available" : @"Not Available",
        @"operationLock": _operationLock ? @"Available" : @"Not Available"
    };

    return [diagnostics copy];
}

- (BOOL)attemptErrorRecovery {
    if (!_lastError) {
        return YES; // 没有错误，认为恢复成功
    }

    BOOL recovered = NO;

    switch (_lastError.result) {
        case HotfixResultNotInitialized:
            // 尝试重新初始化
            recovered = [self initializeWithMemorySize:1024 * 1024]; // 1MB默认内存
            break;

        case HotfixResultRegistrationFailed:
            // 尝试清理并重新初始化方法注册表
            if (_methodRegistry) {
                // 这里可以添加注册表重置逻辑
                recovered = YES;
            }
            break;

        case HotfixResultMemoryError:
            // 尝试重新分配内存
            if (_interpreterCore) {
                // 这里可以添加内存重新分配逻辑
                recovered = YES;
            }
            break;

        case HotfixResultConcurrencyError:
            // 重置并发控制
            _hotfixQueue = dispatch_queue_create("com.hotfix.manager.recovery", DISPATCH_QUEUE_SERIAL);
            _operationLock = [[NSRecursiveLock alloc] init];
            _operationLock.name = @"HotfixManagerRecoveryLock";
            recovered = YES;
            break;

        default:
            // 对于其他错误，尝试基本的系统重置
            recovered = [self validateSystemIntegrity];
            break;
    }

    if (recovered) {
        if (_debugEnabled) {
            NSLog(@"[HotfixManager] Error recovery successful for: %@", _lastError);
        }
        _lastError = nil; // 清除最后错误
    } else {
        if (_debugEnabled) {
            NSLog(@"[HotfixManager] Error recovery failed for: %@", _lastError);
        }
    }

    return recovered;
}

- (BOOL)validateSystemIntegrity {
    // 检查核心组件
    if (!_interpreterCore || !_methodRegistry) {
        [self recordError:HotfixResultUnknownError
                  message:@"核心组件缺失"
               suggestion:@"请重新初始化系统"
                 userInfo:@{@"missingComponents": @[@"interpreterCore", @"methodRegistry"]}];
        return NO;
    }

    // 检查并发控制
    if (!_hotfixQueue || !_operationLock) {
        [self recordError:HotfixResultConcurrencyError
                  message:@"并发控制组件缺失"
               suggestion:@"请重新初始化并发控制"
                 userInfo:@{@"missingConcurrency": @[@"hotfixQueue", @"operationLock"]}];
        return NO;
    }

    // 检查初始化状态
    if (!_initialized) {
        [self recordError:HotfixResultNotInitialized
                  message:@"系统未正确初始化"
               suggestion:@"请调用initializeWithMemorySize:方法"
                 userInfo:nil];
        return NO;
    }

    if (_debugEnabled) {
        NSLog(@"[HotfixManager] System integrity validation passed");
    }

    return YES;
}

@end
