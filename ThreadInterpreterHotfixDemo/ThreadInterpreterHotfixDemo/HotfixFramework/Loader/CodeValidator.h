//
//  CodeValidator.h
//  ThreadInterpreterHotfixDemo
//
//  Created by AI Assistant on 2025-07-31.
//  Copyright © 2025 Demo. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 前向声明
@class AssemblyCodeInfo;

/**
 * 验证级别枚举
 */
typedef NS_ENUM(NSInteger, ValidationLevel) {
    ValidationLevelNone = 0,           // 无验证
    ValidationLevelBasic,              // 基础验证
    ValidationLevelStandard,           // 标准验证
    ValidationLevelStrict,             // 严格验证
    ValidationLevelParanoid            // 偏执验证
};

/**
 * 验证结果枚举
 */
typedef NS_ENUM(NSInteger, ValidationResult) {
    ValidationResultValid = 0,         // 有效
    ValidationResultInvalid,           // 无效
    ValidationResultSuspicious,        // 可疑
    ValidationResultDangerous,         // 危险
    ValidationResultUnknown            // 未知
};

/**
 * 验证错误类型枚举
 */
typedef NS_ENUM(NSInteger, ValidationErrorType) {
    ValidationErrorTypeNone = 0,       // 无错误
    ValidationErrorTypeSyntax,         // 语法错误
    ValidationErrorTypeInstruction,    // 指令错误
    ValidationErrorTypeRegister,       // 寄存器错误
    ValidationErrorTypeMemory,         // 内存访问错误
    ValidationErrorTypeSecurity,       // 安全问题
    ValidationErrorTypePerformance,    // 性能问题
    ValidationErrorTypeCompatibility   // 兼容性问题
};

/**
 * 验证规则
 */
@interface ValidationRule : NSObject

@property (nonatomic, strong, readonly) NSString *ruleId;
@property (nonatomic, strong, readonly) NSString *name;
@property (nonatomic, strong, readonly) NSString *ruleDescription;
@property (nonatomic, assign, readonly) ValidationLevel level;
@property (nonatomic, assign, readonly) ValidationErrorType errorType;
@property (nonatomic, assign) BOOL enabled;

/**
 * 创建验证规则
 */
+ (instancetype)ruleWithId:(NSString *)ruleId
                      name:(NSString *)name
               description:(NSString *)ruleDescription
                     level:(ValidationLevel)level
                 errorType:(ValidationErrorType)errorType;

/**
 * 执行验证
 * @param codeInfo 代码信息
 * @return 验证结果
 */
- (ValidationResult)validateCode:(AssemblyCodeInfo *)codeInfo;

@end

/**
 * 验证报告
 */
@interface ValidationReport : NSObject

@property (nonatomic, assign, readonly) ValidationResult overallResult;
@property (nonatomic, strong, readonly) NSArray<ValidationRule *> *passedRules;
@property (nonatomic, strong, readonly) NSArray<ValidationRule *> *failedRules;
@property (nonatomic, strong, readonly) NSArray<NSString *> *warnings;
@property (nonatomic, strong, readonly) NSArray<NSString *> *errors;
@property (nonatomic, strong, readonly) NSDictionary *statistics;
@property (nonatomic, strong, readonly) NSDate *validatedAt;
@property (nonatomic, assign, readonly) NSTimeInterval validationTime;

/**
 * 创建验证报告
 */
+ (instancetype)reportWithResult:(ValidationResult)result
                     passedRules:(NSArray<ValidationRule *> *)passedRules
                     failedRules:(NSArray<ValidationRule *> *)failedRules
                        warnings:(NSArray<NSString *> *)warnings
                          errors:(NSArray<NSString *> *)errors
                      statistics:(NSDictionary *)statistics
                  validationTime:(NSTimeInterval)validationTime;

/**
 * 是否通过验证
 */
- (BOOL)isValid;

/**
 * 获取详细描述
 */
- (NSString *)detailedDescription;

@end

/**
 * CodeValidator - 代码验证器
 * 
 * 核心功能：
 * 1. 验证汇编代码的语法正确性
 * 2. 检查指令的合法性和安全性
 * 3. 分析代码的性能影响
 * 4. 提供可配置的验证规则
 */
@interface CodeValidator : NSObject

#pragma mark - 初始化

/**
 * 创建代码验证器
 * @param level 验证级别
 * @return 验证器实例
 */
- (instancetype)initWithValidationLevel:(ValidationLevel)level;

#pragma mark - 验证方法

/**
 * 验证汇编代码
 * @param codeInfo 代码信息
 * @return 验证报告
 */
- (ValidationReport *)validateCode:(AssemblyCodeInfo *)codeInfo;

/**
 * 快速验证汇编代码
 * @param codeInfo 代码信息
 * @return 验证结果
 */
- (ValidationResult)quickValidateCode:(AssemblyCodeInfo *)codeInfo;

/**
 * 异步验证汇编代码
 * @param codeInfo 代码信息
 * @param completion 完成回调
 */
- (void)validateCodeAsync:(AssemblyCodeInfo *)codeInfo
               completion:(void (^)(ValidationReport *report))completion;

#pragma mark - 规则管理

/**
 * 添加验证规则
 * @param rule 验证规则
 */
- (void)addValidationRule:(ValidationRule *)rule;

/**
 * 移除验证规则
 * @param ruleId 规则ID
 */
- (void)removeValidationRule:(NSString *)ruleId;

/**
 * 获取验证规则
 * @param ruleId 规则ID
 * @return 验证规则
 */
- (nullable ValidationRule *)getValidationRule:(NSString *)ruleId;

/**
 * 获取所有验证规则
 * @return 规则数组
 */
- (NSArray<ValidationRule *> *)getAllValidationRules;

/**
 * 启用/禁用验证规则
 * @param ruleId 规则ID
 * @param enabled 是否启用
 */
- (void)setValidationRule:(NSString *)ruleId enabled:(BOOL)enabled;

/**
 * 重置为默认规则
 */
- (void)resetToDefaultRules;

#pragma mark - 内置验证规则

/**
 * 语法验证
 * @param codeInfo 代码信息
 * @return 验证结果
 */
- (ValidationResult)validateSyntax:(AssemblyCodeInfo *)codeInfo;

/**
 * 指令验证
 * @param codeInfo 代码信息
 * @return 验证结果
 */
- (ValidationResult)validateInstructions:(AssemblyCodeInfo *)codeInfo;

/**
 * 寄存器使用验证
 * @param codeInfo 代码信息
 * @return 验证结果
 */
- (ValidationResult)validateRegisterUsage:(AssemblyCodeInfo *)codeInfo;

/**
 * 内存访问验证
 * @param codeInfo 代码信息
 * @return 验证结果
 */
- (ValidationResult)validateMemoryAccess:(AssemblyCodeInfo *)codeInfo;

/**
 * 安全性验证
 * @param codeInfo 代码信息
 * @return 验证结果
 */
- (ValidationResult)validateSecurity:(AssemblyCodeInfo *)codeInfo;

/**
 * 性能影响验证
 * @param codeInfo 代码信息
 * @return 验证结果
 */
- (ValidationResult)validatePerformance:(AssemblyCodeInfo *)codeInfo;

#pragma mark - 配置管理

/**
 * 设置验证级别
 * @param level 验证级别
 */
- (void)setValidationLevel:(ValidationLevel)level;

/**
 * 设置最大验证时间
 * @param timeout 超时时间（秒）
 */
- (void)setValidationTimeout:(NSTimeInterval)timeout;

/**
 * 启用/禁用并行验证
 * @param enabled 是否启用
 */
- (void)setParallelValidation:(BOOL)enabled;

/**
 * 设置最大代码大小限制
 * @param maxSize 最大大小（字节）
 */
- (void)setMaxCodeSize:(NSUInteger)maxSize;

#pragma mark - 黑名单和白名单

/**
 * 添加指令到黑名单
 * @param instruction 指令名称
 */
- (void)addBlacklistedInstruction:(NSString *)instruction;

/**
 * 从黑名单移除指令
 * @param instruction 指令名称
 */
- (void)removeBlacklistedInstruction:(NSString *)instruction;

/**
 * 检查指令是否在黑名单中
 * @param instruction 指令名称
 * @return 是否在黑名单中
 */
- (BOOL)isBlacklistedInstruction:(NSString *)instruction;

/**
 * 添加指令到白名单
 * @param instruction 指令名称
 */
- (void)addWhitelistedInstruction:(NSString *)instruction;

/**
 * 从白名单移除指令
 * @param instruction 指令名称
 */
- (void)removeWhitelistedInstruction:(NSString *)instruction;

/**
 * 检查指令是否在白名单中
 * @param instruction 指令名称
 * @return 是否在白名单中
 */
- (BOOL)isWhitelistedInstruction:(NSString *)instruction;

#pragma mark - 统计和调试

/**
 * 启用/禁用调试模式
 * @param enabled 是否启用
 */
- (void)setDebugEnabled:(BOOL)enabled;

/**
 * 获取验证统计信息
 * @return 统计信息字典
 */
- (NSDictionary *)getValidationStatistics;

/**
 * 重置统计信息
 */
- (void)resetStatistics;

/**
 * 打印验证器状态
 */
- (void)printValidatorStatus;

/**
 * 导出配置
 * @return 配置字典
 */
- (NSDictionary *)exportConfiguration;

/**
 * 导入配置
 * @param configuration 配置字典
 * @return 是否成功导入
 */
- (BOOL)importConfiguration:(NSDictionary *)configuration;

#pragma mark - 工具方法

/**
 * 获取验证级别名称
 * @param level 验证级别
 * @return 级别名称
 */
+ (NSString *)nameForValidationLevel:(ValidationLevel)level;

/**
 * 获取验证结果描述
 * @param result 验证结果
 * @return 结果描述
 */
+ (NSString *)descriptionForValidationResult:(ValidationResult)result;

/**
 * 获取错误类型描述
 * @param errorType 错误类型
 * @return 类型描述
 */
+ (NSString *)descriptionForErrorType:(ValidationErrorType)errorType;

#pragma mark - 属性

/**
 * 验证级别
 */
@property (nonatomic, assign) ValidationLevel validationLevel;

/**
 * 验证超时时间
 */
@property (nonatomic, assign) NSTimeInterval validationTimeout;

/**
 * 并行验证
 */
@property (nonatomic, assign) BOOL parallelValidation;

/**
 * 最大代码大小
 */
@property (nonatomic, assign) NSUInteger maxCodeSize;

/**
 * 调试模式
 */
@property (nonatomic, assign) BOOL debugEnabled;

/**
 * 黑名单指令
 */
@property (nonatomic, strong, readonly) NSSet<NSString *> *blacklistedInstructions;

/**
 * 白名单指令
 */
@property (nonatomic, strong, readonly) NSSet<NSString *> *whitelistedInstructions;

@end

NS_ASSUME_NONNULL_END
