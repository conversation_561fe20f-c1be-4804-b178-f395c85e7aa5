//
//  CodeValidator.m
//  ThreadInterpreterHotfixDemo
//
//  Created by AI Assistant on 2025-07-31.
//  Copyright © 2025 Demo. All rights reserved.
//

#import "CodeValidator.h"
#import "AssemblyLoader.h"

#pragma mark - ValidationRule Implementation

@interface ValidationRule ()

@property (nonatomic, strong, readwrite) NSString *ruleId;
@property (nonatomic, strong, readwrite) NSString *name;
@property (nonatomic, strong, readwrite) NSString *ruleDescription;
@property (nonatomic, assign, readwrite) ValidationLevel level;
@property (nonatomic, assign, readwrite) ValidationErrorType errorType;

@end

@implementation ValidationRule

+ (instancetype)ruleWithId:(NSString *)ruleId
                      name:(NSString *)name
               description:(NSString *)ruleDescription
                     level:(ValidationLevel)level
                 errorType:(ValidationErrorType)errorType {

    ValidationRule *rule = [[ValidationRule alloc] init];
    rule.ruleId = ruleId;
    rule.name = name;
    rule.ruleDescription = ruleDescription;
    rule.level = level;
    rule.errorType = errorType;
    rule.enabled = YES;

    return rule;
}

- (ValidationResult)validateCode:(AssemblyCodeInfo *)codeInfo {
    if (!self.enabled || !codeInfo) {
        return ValidationResultValid;
    }
    
    // 基础验证逻辑（子类可以重写）
    switch (self.errorType) {
        case ValidationErrorTypeSyntax:
            return [self validateSyntax:codeInfo];
            
        case ValidationErrorTypeInstruction:
            return [self validateInstructions:codeInfo];
            
        case ValidationErrorTypeRegister:
            return [self validateRegisters:codeInfo];
            
        case ValidationErrorTypeMemory:
            return [self validateMemoryAccess:codeInfo];
            
        case ValidationErrorTypeSecurity:
            return [self validateSecurity:codeInfo];
            
        case ValidationErrorTypePerformance:
            return [self validatePerformance:codeInfo];
            
        case ValidationErrorTypeCompatibility:
            return [self validateCompatibility:codeInfo];
            
        default:
            return ValidationResultValid;
    }
}

- (ValidationResult)validateSyntax:(AssemblyCodeInfo *)codeInfo {
    // 简化的语法验证
    if (codeInfo.format == AssemblyFormatText) {
        NSString *text = [[NSString alloc] initWithData:codeInfo.codeData encoding:NSUTF8StringEncoding];
        if (!text) {
            return ValidationResultInvalid;
        }
        
        // 检查基本语法错误
        NSArray *lines = [text componentsSeparatedByString:@"\n"];
        for (NSString *line in lines) {
            NSString *trimmedLine = [line stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
            if (trimmedLine.length == 0 || [trimmedLine hasPrefix:@";"]) {
                continue; // 空行或注释
            }
            
            // 简单检查：确保行不以非法字符开始
            if ([trimmedLine hasPrefix:@"#"] && ![trimmedLine hasPrefix:@"#include"]) {
                return ValidationResultInvalid;
            }
        }
    }
    
    return ValidationResultValid;
}

- (ValidationResult)validateInstructions:(AssemblyCodeInfo *)codeInfo {
    // 简化的指令验证
    if (codeInfo.format == AssemblyFormatText) {
        NSString *text = [[NSString alloc] initWithData:codeInfo.codeData encoding:NSUTF8StringEncoding];
        if (!text) {
            return ValidationResultInvalid;
        }
        
        // 检查是否包含已知的ARM64指令
        NSArray *validInstructions = @[@"mov", @"add", @"sub", @"mul", @"div", @"ldr", @"str", @"b", @"bl", @"ret", @"nop"];
        NSArray *lines = [text componentsSeparatedByString:@"\n"];
        
        for (NSString *line in lines) {
            NSString *trimmedLine = [line stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
            if (trimmedLine.length == 0 || [trimmedLine hasPrefix:@";"]) {
                continue;
            }
            
            // 提取指令名称
            NSArray *components = [trimmedLine componentsSeparatedByCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
            if (components.count > 0) {
                NSString *instruction = [components[0] lowercaseString];
                if (![validInstructions containsObject:instruction]) {
                    return ValidationResultSuspicious;
                }
            }
        }
    }
    
    return ValidationResultValid;
}

- (ValidationResult)validateRegisters:(AssemblyCodeInfo *)codeInfo {
    // 简化的寄存器验证
    return ValidationResultValid;
}

- (ValidationResult)validateMemoryAccess:(AssemblyCodeInfo *)codeInfo {
    // 简化的内存访问验证
    return ValidationResultValid;
}

- (ValidationResult)validateSecurity:(AssemblyCodeInfo *)codeInfo {
    // 简化的安全验证
    if (codeInfo.format == AssemblyFormatText) {
        NSString *text = [[NSString alloc] initWithData:codeInfo.codeData encoding:NSUTF8StringEncoding];
        if (!text) {
            return ValidationResultInvalid;
        }
        
        // 检查危险指令
        NSArray *dangerousInstructions = @[@"syscall", @"int", @"hlt", @"cli", @"sti"];
        NSString *lowerText = [text lowercaseString];
        
        for (NSString *dangerous in dangerousInstructions) {
            if ([lowerText containsString:dangerous]) {
                return ValidationResultDangerous;
            }
        }
    }
    
    return ValidationResultValid;
}

- (ValidationResult)validatePerformance:(AssemblyCodeInfo *)codeInfo {
    // 简化的性能验证
    if (codeInfo.instructionCount > 10000) {
        return ValidationResultSuspicious;
    }
    
    return ValidationResultValid;
}

- (ValidationResult)validateCompatibility:(AssemblyCodeInfo *)codeInfo {
    // 简化的兼容性验证
    return ValidationResultValid;
}

- (NSString *)description {
    return [NSString stringWithFormat:@"ValidationRule {\n"
            @"  id: %@\n"
            @"  name: %@\n"
            @"  level: %@\n"
            @"  errorType: %@\n"
            @"  enabled: %@\n"
            @"}",
            self.ruleId,
            self.name,
            [CodeValidator nameForValidationLevel:self.level],
            [CodeValidator descriptionForErrorType:self.errorType],
            self.enabled ? @"YES" : @"NO"];
}

@end

#pragma mark - ValidationReport Implementation

@interface ValidationReport ()

@property (nonatomic, assign, readwrite) ValidationResult overallResult;
@property (nonatomic, strong, readwrite) NSArray<ValidationRule *> *passedRules;
@property (nonatomic, strong, readwrite) NSArray<ValidationRule *> *failedRules;
@property (nonatomic, strong, readwrite) NSArray<NSString *> *warnings;
@property (nonatomic, strong, readwrite) NSArray<NSString *> *errors;
@property (nonatomic, strong, readwrite) NSDictionary *statistics;
@property (nonatomic, strong, readwrite) NSDate *validatedAt;
@property (nonatomic, assign, readwrite) NSTimeInterval validationTime;

@end

@implementation ValidationReport

+ (instancetype)reportWithResult:(ValidationResult)result
                     passedRules:(NSArray<ValidationRule *> *)passedRules
                     failedRules:(NSArray<ValidationRule *> *)failedRules
                        warnings:(NSArray<NSString *> *)warnings
                          errors:(NSArray<NSString *> *)errors
                      statistics:(NSDictionary *)statistics
                  validationTime:(NSTimeInterval)validationTime {
    
    ValidationReport *report = [[ValidationReport alloc] init];
    report.overallResult = result;
    report.passedRules = passedRules ?: @[];
    report.failedRules = failedRules ?: @[];
    report.warnings = warnings ?: @[];
    report.errors = errors ?: @[];
    report.statistics = statistics ?: @{};
    report.validatedAt = [NSDate date];
    report.validationTime = validationTime;
    
    return report;
}

- (BOOL)isValid {
    return self.overallResult == ValidationResultValid;
}

- (NSString *)detailedDescription {
    NSMutableString *description = [[NSMutableString alloc] init];
    
    [description appendFormat:@"Validation Report\n"];
    [description appendFormat:@"================\n"];
    [description appendFormat:@"Overall Result: %@\n", [CodeValidator descriptionForValidationResult:self.overallResult]];
    [description appendFormat:@"Validation Time: %.4f seconds\n", self.validationTime];
    [description appendFormat:@"Validated At: %@\n\n", self.validatedAt];
    
    [description appendFormat:@"Rules Summary:\n"];
    [description appendFormat:@"  Passed: %lu\n", (unsigned long)self.passedRules.count];
    [description appendFormat:@"  Failed: %lu\n", (unsigned long)self.failedRules.count];
    [description appendFormat:@"  Warnings: %lu\n", (unsigned long)self.warnings.count];
    [description appendFormat:@"  Errors: %lu\n\n", (unsigned long)self.errors.count];
    
    if (self.failedRules.count > 0) {
        [description appendString:@"Failed Rules:\n"];
        for (ValidationRule *rule in self.failedRules) {
            [description appendFormat:@"  - %@ (%@)\n", rule.name, rule.ruleId];
        }
        [description appendString:@"\n"];
    }
    
    if (self.errors.count > 0) {
        [description appendString:@"Errors:\n"];
        for (NSString *error in self.errors) {
            [description appendFormat:@"  - %@\n", error];
        }
        [description appendString:@"\n"];
    }
    
    if (self.warnings.count > 0) {
        [description appendString:@"Warnings:\n"];
        for (NSString *warning in self.warnings) {
            [description appendFormat:@"  - %@\n", warning];
        }
        [description appendString:@"\n"];
    }
    
    if (self.statistics.count > 0) {
        [description appendString:@"Statistics:\n"];
        for (NSString *key in self.statistics) {
            [description appendFormat:@"  %@: %@\n", key, self.statistics[key]];
        }
    }
    
    return [description copy];
}

@end

#pragma mark - CodeValidator Implementation

@interface CodeValidator ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, ValidationRule *> *validationRules;
@property (nonatomic, strong) NSMutableSet<NSString *> *mutableBlacklistedInstructions;
@property (nonatomic, strong) NSMutableSet<NSString *> *mutableWhitelistedInstructions;
@property (nonatomic, strong) dispatch_queue_t validatorQueue;

// 统计信息
@property (nonatomic, assign) NSUInteger totalValidations;
@property (nonatomic, assign) NSUInteger validCodes;
@property (nonatomic, assign) NSUInteger invalidCodes;
@property (nonatomic, assign) NSUInteger suspiciousCodes;
@property (nonatomic, assign) NSUInteger dangerousCodes;
@property (nonatomic, assign) NSTimeInterval totalValidationTime;

@end

@implementation CodeValidator

#pragma mark - 初始化

- (instancetype)init {
    return [self initWithValidationLevel:ValidationLevelStandard];
}

- (instancetype)initWithValidationLevel:(ValidationLevel)level {
    self = [super init];
    if (self) {
        _validationLevel = level;
        _validationTimeout = 30.0;
        _parallelValidation = YES;
        _maxCodeSize = 1024 * 1024; // 1MB
        _debugEnabled = NO;

        _validationRules = [[NSMutableDictionary alloc] init];
        _mutableBlacklistedInstructions = [[NSMutableSet alloc] init];
        _mutableWhitelistedInstructions = [[NSMutableSet alloc] init];
        _validatorQueue = dispatch_queue_create("com.hotfix.codevalidator", DISPATCH_QUEUE_CONCURRENT);

        // 统计信息初始化
        _totalValidations = 0;
        _validCodes = 0;
        _invalidCodes = 0;
        _suspiciousCodes = 0;
        _dangerousCodes = 0;
        _totalValidationTime = 0.0;

        // 设置默认规则
        [self resetToDefaultRules];

        // 设置默认黑名单
        [self setupDefaultBlacklist];

        // 设置默认白名单
        [self setupDefaultWhitelist];
    }
    return self;
}

- (NSSet<NSString *> *)blacklistedInstructions {
    return [_mutableBlacklistedInstructions copy];
}

- (NSSet<NSString *> *)whitelistedInstructions {
    return [_mutableWhitelistedInstructions copy];
}

#pragma mark - 验证方法

- (ValidationReport *)validateCode:(AssemblyCodeInfo *)codeInfo {
    if (!codeInfo) {
        return [ValidationReport reportWithResult:ValidationResultInvalid
                                      passedRules:@[]
                                      failedRules:@[]
                                         warnings:@[]
                                           errors:@[@"No code info provided"]
                                       statistics:@{}
                                   validationTime:0.0];
    }

    NSDate *startTime = [NSDate date];
    self.totalValidations++;

    // 检查代码大小限制
    if (codeInfo.codeSize > self.maxCodeSize) {
        self.invalidCodes++;
        return [ValidationReport reportWithResult:ValidationResultInvalid
                                      passedRules:@[]
                                      failedRules:@[]
                                         warnings:@[]
                                           errors:@[@"Code size exceeds maximum limit"]
                                       statistics:@{@"codeSize": @(codeInfo.codeSize), @"maxSize": @(self.maxCodeSize)}
                                   validationTime:[[NSDate date] timeIntervalSinceDate:startTime]];
    }

    NSMutableArray<ValidationRule *> *passedRules = [[NSMutableArray alloc] init];
    NSMutableArray<ValidationRule *> *failedRules = [[NSMutableArray alloc] init];
    NSMutableArray<NSString *> *warnings = [[NSMutableArray alloc] init];
    NSMutableArray<NSString *> *errors = [[NSMutableArray alloc] init];

    ValidationResult overallResult = ValidationResultValid;

    // 执行所有启用的验证规则
    NSArray<ValidationRule *> *rulesToValidate = [self getRulesForLevel:self.validationLevel];

    if (self.parallelValidation && rulesToValidate.count > 1) {
        // 并行验证
        dispatch_group_t group = dispatch_group_create();
        __block ValidationResult worstResult = ValidationResultValid;

        for (ValidationRule *rule in rulesToValidate) {
            if (!rule.enabled) continue;

            dispatch_group_async(group, _validatorQueue, ^{
                ValidationResult result = [rule validateCode:codeInfo];

                dispatch_barrier_async(self.validatorQueue, ^{
                    if (result == ValidationResultValid) {
                        [passedRules addObject:rule];
                    } else {
                        [failedRules addObject:rule];

                        // 更新最差结果
                        if (result > worstResult) {
                            worstResult = result;
                        }

                        // 添加错误或警告
                        NSString *message = [NSString stringWithFormat:@"Rule '%@' failed", rule.name];
                        if (result == ValidationResultDangerous) {
                            [errors addObject:message];
                        } else {
                            [warnings addObject:message];
                        }
                    }
                });
            });
        }

        // 等待所有验证完成
        dispatch_group_wait(group, dispatch_time(DISPATCH_TIME_NOW, (int64_t)(self.validationTimeout * NSEC_PER_SEC)));
        overallResult = worstResult;

    } else {
        // 串行验证
        for (ValidationRule *rule in rulesToValidate) {
            if (!rule.enabled) continue;

            ValidationResult result = [rule validateCode:codeInfo];

            if (result == ValidationResultValid) {
                [passedRules addObject:rule];
            } else {
                [failedRules addObject:rule];

                // 更新整体结果
                if (result > overallResult) {
                    overallResult = result;
                }

                // 添加错误或警告
                NSString *message = [NSString stringWithFormat:@"Rule '%@' failed", rule.name];
                if (result == ValidationResultDangerous) {
                    [errors addObject:message];
                } else {
                    [warnings addObject:message];
                }
            }
        }
    }

    // 更新统计信息
    NSTimeInterval validationTime = [[NSDate date] timeIntervalSinceDate:startTime];
    self.totalValidationTime += validationTime;

    switch (overallResult) {
        case ValidationResultValid:
            self.validCodes++;
            break;
        case ValidationResultInvalid:
            self.invalidCodes++;
            break;
        case ValidationResultSuspicious:
            self.suspiciousCodes++;
            break;
        case ValidationResultDangerous:
            self.dangerousCodes++;
            break;
        default:
            break;
    }

    // 创建统计信息
    NSDictionary *statistics = @{
        @"rulesExecuted": @(passedRules.count + failedRules.count),
        @"rulesPassed": @(passedRules.count),
        @"rulesFailed": @(failedRules.count),
        @"codeSize": @(codeInfo.codeSize),
        @"instructionCount": @(codeInfo.instructionCount),
        @"validationLevel": @(self.validationLevel)
    };

    if (self.debugEnabled) {
        NSLog(@"[CodeValidator] Validation completed in %.4f seconds, result: %@",
              validationTime, [CodeValidator descriptionForValidationResult:overallResult]);
    }

    return [ValidationReport reportWithResult:overallResult
                                  passedRules:[passedRules copy]
                                  failedRules:[failedRules copy]
                                     warnings:[warnings copy]
                                       errors:[errors copy]
                                   statistics:statistics
                               validationTime:validationTime];
}

- (ValidationResult)quickValidateCode:(AssemblyCodeInfo *)codeInfo {
    if (!codeInfo) {
        return ValidationResultInvalid;
    }

    // 快速验证：只执行基础检查
    if (codeInfo.codeSize > self.maxCodeSize) {
        return ValidationResultInvalid;
    }

    // 执行基础验证规则
    NSArray<ValidationRule *> *basicRules = [self getRulesForLevel:ValidationLevelBasic];
    ValidationResult worstResult = ValidationResultValid;

    for (ValidationRule *rule in basicRules) {
        if (!rule.enabled) continue;

        ValidationResult result = [rule validateCode:codeInfo];
        if (result > worstResult) {
            worstResult = result;
        }

        // 如果发现危险代码，立即返回
        if (result == ValidationResultDangerous) {
            break;
        }
    }

    return worstResult;
}

- (void)validateCodeAsync:(AssemblyCodeInfo *)codeInfo
               completion:(void (^)(ValidationReport *report))completion {

    if (!completion) {
        return;
    }

    dispatch_async(_validatorQueue, ^{
        ValidationReport *report = [self validateCode:codeInfo];

        dispatch_async(dispatch_get_main_queue(), ^{
            completion(report);
        });
    });
}

#pragma mark - 规则管理

- (void)addValidationRule:(ValidationRule *)rule {
    if (!rule || !rule.ruleId) {
        return;
    }

    dispatch_barrier_async(_validatorQueue, ^{
        self.validationRules[rule.ruleId] = rule;

        if (self.debugEnabled) {
            NSLog(@"[CodeValidator] Added validation rule: %@", rule.name);
        }
    });
}

- (void)removeValidationRule:(NSString *)ruleId {
    if (!ruleId) {
        return;
    }

    dispatch_barrier_async(_validatorQueue, ^{
        [self.validationRules removeObjectForKey:ruleId];

        if (self.debugEnabled) {
            NSLog(@"[CodeValidator] Removed validation rule: %@", ruleId);
        }
    });
}

- (nullable ValidationRule *)getValidationRule:(NSString *)ruleId {
    if (!ruleId) {
        return nil;
    }

    __block ValidationRule *rule = nil;

    dispatch_sync(_validatorQueue, ^{
        rule = self.validationRules[ruleId];
    });

    return rule;
}

- (NSArray<ValidationRule *> *)getAllValidationRules {
    __block NSArray<ValidationRule *> *rules = nil;

    dispatch_sync(_validatorQueue, ^{
        rules = [self.validationRules.allValues copy];
    });

    return rules;
}

- (void)setValidationRule:(NSString *)ruleId enabled:(BOOL)enabled {
    ValidationRule *rule = [self getValidationRule:ruleId];
    if (rule) {
        rule.enabled = enabled;

        if (self.debugEnabled) {
            NSLog(@"[CodeValidator] Rule '%@' %@", ruleId, enabled ? @"enabled" : @"disabled");
        }
    }
}

- (void)resetToDefaultRules {
    dispatch_barrier_async(_validatorQueue, ^{
        [self.validationRules removeAllObjects];

        // 添加默认验证规则
        [self addDefaultRule:@"syntax_check"
                        name:@"Syntax Check"
                 description:@"Basic syntax validation"
                       level:ValidationLevelBasic
                   errorType:ValidationErrorTypeSyntax];

        [self addDefaultRule:@"instruction_check"
                        name:@"Instruction Check"
                 description:@"Validate ARM64 instructions"
                       level:ValidationLevelStandard
                   errorType:ValidationErrorTypeInstruction];

        [self addDefaultRule:@"register_check"
                        name:@"Register Check"
                 description:@"Validate register usage"
                       level:ValidationLevelStandard
                   errorType:ValidationErrorTypeRegister];

        [self addDefaultRule:@"memory_check"
                        name:@"Memory Access Check"
                 description:@"Validate memory access patterns"
                       level:ValidationLevelStrict
                   errorType:ValidationErrorTypeMemory];

        [self addDefaultRule:@"security_check"
                        name:@"Security Check"
                 description:@"Check for dangerous instructions"
                       level:ValidationLevelBasic
                   errorType:ValidationErrorTypeSecurity];

        [self addDefaultRule:@"performance_check"
                        name:@"Performance Check"
                 description:@"Analyze performance impact"
                       level:ValidationLevelStrict
                   errorType:ValidationErrorTypePerformance];

        [self addDefaultRule:@"compatibility_check"
                        name:@"Compatibility Check"
                 description:@"Check ARM64 compatibility"
                       level:ValidationLevelParanoid
                   errorType:ValidationErrorTypeCompatibility];

        if (self.debugEnabled) {
            NSLog(@"[CodeValidator] Reset to default rules");
        }
    });
}

- (void)addDefaultRule:(NSString *)ruleId
                  name:(NSString *)name
           description:(NSString *)description
                 level:(ValidationLevel)level
             errorType:(ValidationErrorType)errorType {

    ValidationRule *rule = [ValidationRule ruleWithId:ruleId
                                                  name:name
                                           description:description
                                                 level:level
                                             errorType:errorType];
    self.validationRules[ruleId] = rule;
}

- (NSArray<ValidationRule *> *)getRulesForLevel:(ValidationLevel)level {
    NSMutableArray<ValidationRule *> *rules = [[NSMutableArray alloc] init];

    for (ValidationRule *rule in self.validationRules.allValues) {
        if (rule.enabled && rule.level <= level) {
            [rules addObject:rule];
        }
    }

    return [rules copy];
}

#pragma mark - 内置验证规则

- (ValidationResult)validateSyntax:(AssemblyCodeInfo *)codeInfo {
    ValidationRule *rule = [self getValidationRule:@"syntax_check"];
    return rule ? [rule validateCode:codeInfo] : ValidationResultValid;
}

- (ValidationResult)validateInstructions:(AssemblyCodeInfo *)codeInfo {
    ValidationRule *rule = [self getValidationRule:@"instruction_check"];
    return rule ? [rule validateCode:codeInfo] : ValidationResultValid;
}

- (ValidationResult)validateRegisterUsage:(AssemblyCodeInfo *)codeInfo {
    ValidationRule *rule = [self getValidationRule:@"register_check"];
    return rule ? [rule validateCode:codeInfo] : ValidationResultValid;
}

- (ValidationResult)validateMemoryAccess:(AssemblyCodeInfo *)codeInfo {
    ValidationRule *rule = [self getValidationRule:@"memory_check"];
    return rule ? [rule validateCode:codeInfo] : ValidationResultValid;
}

- (ValidationResult)validateSecurity:(AssemblyCodeInfo *)codeInfo {
    ValidationRule *rule = [self getValidationRule:@"security_check"];
    return rule ? [rule validateCode:codeInfo] : ValidationResultValid;
}

- (ValidationResult)validatePerformance:(AssemblyCodeInfo *)codeInfo {
    ValidationRule *rule = [self getValidationRule:@"performance_check"];
    return rule ? [rule validateCode:codeInfo] : ValidationResultValid;
}

#pragma mark - 配置管理

- (void)setValidationLevel:(ValidationLevel)level {
    _validationLevel = level;

    if (self.debugEnabled) {
        NSLog(@"[CodeValidator] Validation level set to: %@", [CodeValidator nameForValidationLevel:level]);
    }
}

- (void)setValidationTimeout:(NSTimeInterval)timeout {
    _validationTimeout = MAX(timeout, 1.0); // 最小1秒
}

- (void)setParallelValidation:(BOOL)enabled {
    _parallelValidation = enabled;

    if (self.debugEnabled) {
        NSLog(@"[CodeValidator] Parallel validation %@", enabled ? @"enabled" : @"disabled");
    }
}

- (void)setMaxCodeSize:(NSUInteger)maxSize {
    _maxCodeSize = maxSize;

    if (self.debugEnabled) {
        NSLog(@"[CodeValidator] Max code size set to: %lu bytes", (unsigned long)maxSize);
    }
}

#pragma mark - 黑名单和白名单

- (void)addBlacklistedInstruction:(NSString *)instruction {
    if (instruction && instruction.length > 0) {
        [_mutableBlacklistedInstructions addObject:[instruction lowercaseString]];

        if (self.debugEnabled) {
            NSLog(@"[CodeValidator] Added blacklisted instruction: %@", instruction);
        }
    }
}

- (void)removeBlacklistedInstruction:(NSString *)instruction {
    if (instruction) {
        [_mutableBlacklistedInstructions removeObject:[instruction lowercaseString]];

        if (self.debugEnabled) {
            NSLog(@"[CodeValidator] Removed blacklisted instruction: %@", instruction);
        }
    }
}

- (BOOL)isBlacklistedInstruction:(NSString *)instruction {
    return instruction && [_mutableBlacklistedInstructions containsObject:[instruction lowercaseString]];
}

- (void)addWhitelistedInstruction:(NSString *)instruction {
    if (instruction && instruction.length > 0) {
        [_mutableWhitelistedInstructions addObject:[instruction lowercaseString]];

        if (self.debugEnabled) {
            NSLog(@"[CodeValidator] Added whitelisted instruction: %@", instruction);
        }
    }
}

- (void)removeWhitelistedInstruction:(NSString *)instruction {
    if (instruction) {
        [_mutableWhitelistedInstructions removeObject:[instruction lowercaseString]];

        if (self.debugEnabled) {
            NSLog(@"[CodeValidator] Removed whitelisted instruction: %@", instruction);
        }
    }
}

- (BOOL)isWhitelistedInstruction:(NSString *)instruction {
    return instruction && [_mutableWhitelistedInstructions containsObject:[instruction lowercaseString]];
}

- (void)setupDefaultBlacklist {
    // 危险的系统调用和特权指令
    NSArray *dangerousInstructions = @[
        @"syscall", @"svc", @"hvc", @"smc",  // 系统调用
        @"msr", @"mrs",                      // 系统寄存器访问
        @"tlbi", @"ic", @"dc",              // 缓存和TLB操作
        @"isb", @"dsb", @"dmb",             // 内存屏障
        @"wfi", @"wfe", @"sev", @"sevl",    // 等待和事件指令
        @"brk", @"hlt",                     // 断点和停机
        @"eret", @"drps"                    // 异常返回
    ];

    for (NSString *instruction in dangerousInstructions) {
        [_mutableBlacklistedInstructions addObject:instruction];
    }
}

- (void)setupDefaultWhitelist {
    // 安全的基础ARM64指令
    NSArray *safeInstructions = @[
        // 数据处理指令
        @"add", @"sub", @"mul", @"div", @"mod",
        @"and", @"orr", @"eor", @"bic",
        @"lsl", @"lsr", @"asr", @"ror",
        @"mov", @"mvn",

        // 内存访问指令
        @"ldr", @"str", @"ldp", @"stp",
        @"ldrb", @"strb", @"ldrh", @"strh",

        // 分支指令
        @"b", @"bl", @"br", @"blr",
        @"ret", @"cbz", @"cbnz",
        @"tbz", @"tbnz",

        // 比较指令
        @"cmp", @"cmn", @"tst",

        // 条件指令
        @"csel", @"cset", @"cinc", @"cneg",

        // 其他安全指令
        @"nop", @"hint"
    ];

    for (NSString *instruction in safeInstructions) {
        [_mutableWhitelistedInstructions addObject:instruction];
    }
}

#pragma mark - 统计和调试

- (void)setDebugEnabled:(BOOL)enabled {
    _debugEnabled = enabled;
}

- (NSDictionary *)getValidationStatistics {
    return @{
        @"totalValidations": @(self.totalValidations),
        @"validCodes": @(self.validCodes),
        @"invalidCodes": @(self.invalidCodes),
        @"suspiciousCodes": @(self.suspiciousCodes),
        @"dangerousCodes": @(self.dangerousCodes),
        @"validRate": @(self.totalValidations > 0 ? (double)self.validCodes / self.totalValidations : 0.0),
        @"totalValidationTime": @(self.totalValidationTime),
        @"averageValidationTime": @(self.totalValidations > 0 ? self.totalValidationTime / self.totalValidations : 0.0),
        @"rulesCount": @(self.validationRules.count),
        @"blacklistedInstructions": @(self.blacklistedInstructions.count),
        @"whitelistedInstructions": @(self.whitelistedInstructions.count)
    };
}

- (void)resetStatistics {
    dispatch_barrier_async(_validatorQueue, ^{
        self.totalValidations = 0;
        self.validCodes = 0;
        self.invalidCodes = 0;
        self.suspiciousCodes = 0;
        self.dangerousCodes = 0;
        self.totalValidationTime = 0.0;
    });

    if (self.debugEnabled) {
        NSLog(@"[CodeValidator] Statistics reset");
    }
}

- (void)printValidatorStatus {
    NSLog(@"[CodeValidator] Validator Status:");
    NSLog(@"  Validation Level: %@", [CodeValidator nameForValidationLevel:self.validationLevel]);
    NSLog(@"  Validation Timeout: %.2f seconds", self.validationTimeout);
    NSLog(@"  Parallel Validation: %@", self.parallelValidation ? @"YES" : @"NO");
    NSLog(@"  Max Code Size: %lu bytes", (unsigned long)self.maxCodeSize);
    NSLog(@"  Debug Enabled: %@", self.debugEnabled ? @"YES" : @"NO");
    NSLog(@"  Validation Rules: %lu", (unsigned long)self.validationRules.count);
    NSLog(@"  Blacklisted Instructions: %lu", (unsigned long)self.blacklistedInstructions.count);
    NSLog(@"  Whitelisted Instructions: %lu", (unsigned long)self.whitelistedInstructions.count);
    NSLog(@"  Statistics: %@", [self getValidationStatistics]);
}

- (NSDictionary *)exportConfiguration {
    NSMutableDictionary *config = [[NSMutableDictionary alloc] init];

    config[@"validationLevel"] = @(self.validationLevel);
    config[@"validationTimeout"] = @(self.validationTimeout);
    config[@"parallelValidation"] = @(self.parallelValidation);
    config[@"maxCodeSize"] = @(self.maxCodeSize);
    config[@"debugEnabled"] = @(self.debugEnabled);

    // 导出规则配置
    NSMutableDictionary *rulesConfig = [[NSMutableDictionary alloc] init];
    for (NSString *ruleId in self.validationRules) {
        ValidationRule *rule = self.validationRules[ruleId];
        rulesConfig[ruleId] = @{
            @"name": rule.name,
            @"description": rule.ruleDescription,
            @"level": @(rule.level),
            @"errorType": @(rule.errorType),
            @"enabled": @(rule.enabled)
        };
    }
    config[@"rules"] = rulesConfig;

    config[@"blacklistedInstructions"] = [self.blacklistedInstructions allObjects];
    config[@"whitelistedInstructions"] = [self.whitelistedInstructions allObjects];

    return [config copy];
}

- (BOOL)importConfiguration:(NSDictionary *)configuration {
    if (!configuration) {
        return NO;
    }

    @try {
        // 导入基本配置
        if (configuration[@"validationLevel"]) {
            self.validationLevel = [configuration[@"validationLevel"] integerValue];
        }
        if (configuration[@"validationTimeout"]) {
            self.validationTimeout = [configuration[@"validationTimeout"] doubleValue];
        }
        if (configuration[@"parallelValidation"]) {
            self.parallelValidation = [configuration[@"parallelValidation"] boolValue];
        }
        if (configuration[@"maxCodeSize"]) {
            self.maxCodeSize = [configuration[@"maxCodeSize"] unsignedIntegerValue];
        }
        if (configuration[@"debugEnabled"]) {
            self.debugEnabled = [configuration[@"debugEnabled"] boolValue];
        }

        // 导入黑白名单
        if (configuration[@"blacklistedInstructions"]) {
            [_mutableBlacklistedInstructions removeAllObjects];
            NSArray *blacklist = configuration[@"blacklistedInstructions"];
            for (NSString *instruction in blacklist) {
                [self addBlacklistedInstruction:instruction];
            }
        }

        if (configuration[@"whitelistedInstructions"]) {
            [_mutableWhitelistedInstructions removeAllObjects];
            NSArray *whitelist = configuration[@"whitelistedInstructions"];
            for (NSString *instruction in whitelist) {
                [self addWhitelistedInstruction:instruction];
            }
        }

        if (self.debugEnabled) {
            NSLog(@"[CodeValidator] Configuration imported successfully");
        }

        return YES;

    } @catch (NSException *exception) {
        if (self.debugEnabled) {
            NSLog(@"[CodeValidator] Failed to import configuration: %@", exception.reason);
        }
        return NO;
    }
}

#pragma mark - 工具方法

+ (NSString *)nameForValidationLevel:(ValidationLevel)level {
    switch (level) {
        case ValidationLevelNone: return @"None";
        case ValidationLevelBasic: return @"Basic";
        case ValidationLevelStandard: return @"Standard";
        case ValidationLevelStrict: return @"Strict";
        case ValidationLevelParanoid: return @"Paranoid";
        default: return @"Unknown";
    }
}

+ (NSString *)descriptionForValidationResult:(ValidationResult)result {
    switch (result) {
        case ValidationResultValid: return @"Valid";
        case ValidationResultInvalid: return @"Invalid";
        case ValidationResultSuspicious: return @"Suspicious";
        case ValidationResultDangerous: return @"Dangerous";
        case ValidationResultUnknown: return @"Unknown";
        default: return @"Invalid Result";
    }
}

+ (NSString *)descriptionForErrorType:(ValidationErrorType)errorType {
    switch (errorType) {
        case ValidationErrorTypeNone: return @"None";
        case ValidationErrorTypeSyntax: return @"Syntax Error";
        case ValidationErrorTypeInstruction: return @"Instruction Error";
        case ValidationErrorTypeRegister: return @"Register Error";
        case ValidationErrorTypeMemory: return @"Memory Access Error";
        case ValidationErrorTypeSecurity: return @"Security Issue";
        case ValidationErrorTypePerformance: return @"Performance Issue";
        case ValidationErrorTypeCompatibility: return @"Compatibility Issue";
        default: return @"Unknown Error";
    }
}

@end
