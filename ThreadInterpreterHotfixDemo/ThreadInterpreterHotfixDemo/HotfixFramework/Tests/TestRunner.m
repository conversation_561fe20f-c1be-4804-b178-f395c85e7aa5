//
//  TestRunner.m
//  ThreadInterpreterHotfixDemo
//
//  Created by AI Assistant on 2025-07-31.
//  Copyright © 2025 Demo. All rights reserved.
//

#import "TestRunner.h"
#import "HotfixIntegrationTests.h"

#pragma mark - TestRunnerConfiguration Implementation

@implementation TestRunnerConfiguration

+ (instancetype)defaultConfiguration {
    TestRunnerConfiguration *config = [[TestRunnerConfiguration alloc] init];
    config.verboseLogging = NO;
    config.testTimeout = 30.0;
    config.performanceTestsEnabled = YES;
    config.stabilityTestsEnabled = YES;
    config.stopOnFailure = NO;
    config.generateDetailedReport = YES;
    config.reportOutputPath = nil;
    return config;
}

+ (instancetype)quickTestConfiguration {
    TestRunnerConfiguration *config = [[TestRunnerConfiguration alloc] init];
    config.verboseLogging = NO;
    config.testTimeout = 10.0;
    config.performanceTestsEnabled = NO;
    config.stabilityTestsEnabled = NO;
    config.stopOnFailure = NO;
    config.generateDetailedReport = NO;
    config.reportOutputPath = nil;
    return config;
}

+ (instancetype)fullTestConfiguration {
    TestRunnerConfiguration *config = [[TestRunnerConfiguration alloc] init];
    config.verboseLogging = YES;
    config.testTimeout = 60.0;
    config.performanceTestsEnabled = YES;
    config.stabilityTestsEnabled = YES;
    config.stopOnFailure = NO;
    config.generateDetailedReport = YES;
    config.reportOutputPath = @"/tmp/hotfix_test_report.html";
    return config;
}

@end

#pragma mark - TestRunner Implementation

@interface TestRunner ()

@property (nonatomic, strong, readwrite) TestRunnerConfiguration *configuration;
@property (nonatomic, strong, readwrite) HotfixIntegrationTests *integrationTests;
@property (nonatomic, assign, readwrite) BOOL isRunning;
@property (nonatomic, strong, readwrite, nullable) NSString *currentTestId;
@property (nonatomic, strong, readwrite, nullable) NSDate *testStartTime;

@end

@implementation TestRunner

#pragma mark - 初始化

+ (instancetype)runnerWithConfiguration:(TestRunnerConfiguration *)configuration {
    TestRunner *runner = [[TestRunner alloc] init];
    runner.configuration = configuration ?: [TestRunnerConfiguration defaultConfiguration];
    [runner setupIntegrationTests];
    return runner;
}

+ (instancetype)defaultRunner {
    return [self runnerWithConfiguration:[TestRunnerConfiguration defaultConfiguration]];
}

- (instancetype)init {
    return [self initWithConfiguration:[TestRunnerConfiguration defaultConfiguration]];
}

- (instancetype)initWithConfiguration:(TestRunnerConfiguration *)configuration {
    self = [super init];
    if (self) {
        _isRunning = NO;
        _currentTestId = nil;
        _testStartTime = nil;
        _configuration = configuration ?: [TestRunnerConfiguration defaultConfiguration];
        [self setupIntegrationTests];
    }
    return self;
}

- (void)setupIntegrationTests {
    _integrationTests = [HotfixIntegrationTests sharedInstance];
    
    // 应用配置
    _integrationTests.verboseLogging = _configuration.verboseLogging;
    _integrationTests.testTimeout = _configuration.testTimeout;
    _integrationTests.performanceTestsEnabled = _configuration.performanceTestsEnabled;
    _integrationTests.stabilityTestsEnabled = _configuration.stabilityTestsEnabled;
}

#pragma mark - 测试执行

- (TestReport *)runAllTests {
    if (_isRunning) {
        NSLog(@"[TestRunner] Tests are already running");
        return nil;
    }
    
    _isRunning = YES;
    _testStartTime = [NSDate date];
    _currentTestId = @"all_tests";
    
    if (_configuration.verboseLogging) {
        NSLog(@"[TestRunner] Starting all tests...");
        [_integrationTests printTestEnvironment];
    }
    
    // 验证测试环境
    if (![self validateTestEnvironment]) {
        NSLog(@"[TestRunner] Test environment validation failed");
        _isRunning = NO;
        return nil;
    }
    
    TestReport *report = [_integrationTests runAllTests];
    
    _isRunning = NO;
    _currentTestId = nil;
    
    if (_configuration.generateDetailedReport) {
        [self printConsoleReport:report];
        
        if (_configuration.reportOutputPath) {
            [self generateHTMLReport:report toPath:_configuration.reportOutputPath];
        }
    }
    
    if (_configuration.verboseLogging) {
        NSLog(@"[TestRunner] All tests completed in %.4f seconds", 
              [[NSDate date] timeIntervalSinceDate:_testStartTime]);
    }
    
    return report;
}

- (TestReport *)runTests:(NSArray<NSString *> *)testIds {
    if (_isRunning) {
        NSLog(@"[TestRunner] Tests are already running");
        return nil;
    }
    
    if (!testIds || testIds.count == 0) {
        return [self runAllTests];
    }
    
    _isRunning = YES;
    _testStartTime = [NSDate date];
    _currentTestId = [testIds componentsJoinedByString:@","];
    
    if (_configuration.verboseLogging) {
        NSLog(@"[TestRunner] Starting tests: %@", _currentTestId);
    }
    
    TestReport *report = [_integrationTests runTests:testIds];
    
    _isRunning = NO;
    _currentTestId = nil;
    
    if (_configuration.generateDetailedReport) {
        [self printConsoleReport:report];
    }
    
    return report;
}

- (void)runAllTestsAsync:(void (^)(TestReport *report))completion {
    if (!completion) {
        return;
    }
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        TestReport *report = [self runAllTests];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            completion(report);
        });
    });
}

- (void)runTestsAsync:(NSArray<NSString *> *)testIds
           completion:(void (^)(TestReport *report))completion {
    
    if (!completion) {
        return;
    }
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        TestReport *report = [self runTests:testIds];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            completion(report);
        });
    });
}

#pragma mark - 快速测试

- (TestReport *)runQuickTests {
    NSArray *quickTestIds = @[
        @"hotfix_manager_basics",
        @"method_replacement",
        @"method_restoration",
        @"assembly_loader",
        @"code_validator"
    ];
    
    return [self runTests:quickTestIds];
}

- (TestReport *)runComponentTests {
    NSArray *componentTestIds = @[
        @"hotfix_manager_basics",
        @"method_registry",
        @"method_hook",
        @"parameter_mapper",
        @"runtime_bridge",
        @"assembly_loader",
        @"code_validator"
    ];
    
    return [self runTests:componentTestIds];
}

- (TestReport *)runIntegrationTests {
    NSArray *integrationTestIds = @[
        @"complete_hotfix_flow",
        @"multiple_method_replacement",
        @"nested_method_calls",
        @"exception_handling"
    ];
    
    return [self runTests:integrationTestIds];
}

- (TestReport *)runPerformanceTests {
    if (!_configuration.performanceTestsEnabled) {
        NSLog(@"[TestRunner] Performance tests are disabled");
        return nil;
    }
    
    NSArray *performanceTestIds = @[
        @"method_replacement_performance",
        @"memory_usage",
        @"concurrency_safety"
    ];
    
    return [self runTests:performanceTestIds];
}

- (TestReport *)runStabilityTests {
    if (!_configuration.stabilityTestsEnabled) {
        NSLog(@"[TestRunner] Stability tests are disabled");
        return nil;
    }
    
    NSArray *stabilityTestIds = @[
        @"long_running_stability",
        @"high_volume_stability",
        @"error_recovery"
    ];
    
    return [self runTests:stabilityTestIds];
}

#pragma mark - 报告生成

- (BOOL)generateHTMLReport:(TestReport *)report toPath:(NSString *)outputPath {
    if (!report || !outputPath) {
        return NO;
    }
    
    // 简化实现：生成基本HTML报告
    NSMutableString *html = [[NSMutableString alloc] init];
    
    [html appendString:@"<!DOCTYPE html>\n"];
    [html appendString:@"<html><head><title>Hotfix Test Report</title></head><body>\n"];
    [html appendFormat:@"<h1>Hotfix Integration Test Report</h1>\n"];
    [html appendFormat:@"<p>Generated: %@</p>\n", report.reportGeneratedAt];
    [html appendFormat:@"<p>Total Tests: %lu</p>\n", (unsigned long)report.totalTests];
    [html appendFormat:@"<p>Passed: %lu</p>\n", (unsigned long)report.passedTests];
    [html appendFormat:@"<p>Failed: %lu</p>\n", (unsigned long)report.failedTests];
    [html appendFormat:@"<p>Success Rate: %.2f%%</p>\n", report.successRate * 100.0];
    [html appendString:@"</body></html>\n"];
    
    NSError *error = nil;
    BOOL success = [html writeToFile:outputPath
                          atomically:YES
                            encoding:NSUTF8StringEncoding
                               error:&error];
    
    if (!success && _configuration.verboseLogging) {
        NSLog(@"[TestRunner] Failed to write HTML report: %@", error.localizedDescription);
    }
    
    return success;
}

- (BOOL)generateJSONReport:(TestReport *)report toPath:(NSString *)outputPath {
    if (!report || !outputPath) {
        return NO;
    }
    
    NSDictionary *jsonData = [report exportToJSON];
    
    NSError *error = nil;
    NSData *jsonBytes = [NSJSONSerialization dataWithJSONObject:jsonData
                                                        options:NSJSONWritingPrettyPrinted
                                                          error:&error];
    
    if (!jsonBytes) {
        if (_configuration.verboseLogging) {
            NSLog(@"[TestRunner] Failed to serialize JSON: %@", error.localizedDescription);
        }
        return NO;
    }
    
    BOOL success = [jsonBytes writeToFile:outputPath atomically:YES];
    
    if (!success && _configuration.verboseLogging) {
        NSLog(@"[TestRunner] Failed to write JSON report to: %@", outputPath);
    }
    
    return success;
}

- (void)printConsoleReport:(TestReport *)report {
    if (!report) {
        return;
    }
    
    NSLog(@"%@", report.detailedDescription);
}

#pragma mark - 测试分析

- (NSDictionary *)analyzeTestResults:(TestReport *)report {
    if (!report) {
        return @{};
    }

    NSMutableDictionary *analysis = [[NSMutableDictionary alloc] init];

    // 基本统计
    analysis[@"summary"] = @{
        @"totalTests": @(report.totalTests),
        @"passedTests": @(report.passedTests),
        @"failedTests": @(report.failedTests),
        @"skippedTests": @(report.skippedTests),
        @"errorTests": @(report.errorTests),
        @"successRate": @(report.successRate),
        @"totalExecutionTime": @(report.totalExecutionTime)
    };

    // 性能分析
    NSTimeInterval averageTime = report.totalTests > 0 ? report.totalExecutionTime / report.totalTests : 0.0;
    NSTimeInterval maxTime = 0.0;
    NSTimeInterval minTime = INFINITY;

    for (TestCase *testCase in report.testCases) {
        if (testCase.executionTime > maxTime) {
            maxTime = testCase.executionTime;
        }
        if (testCase.executionTime < minTime) {
            minTime = testCase.executionTime;
        }
    }

    analysis[@"performance"] = @{
        @"averageExecutionTime": @(averageTime),
        @"maxExecutionTime": @(maxTime),
        @"minExecutionTime": @(minTime == INFINITY ? 0.0 : minTime)
    };

    // 失败分析
    NSMutableArray *failedTests = [[NSMutableArray alloc] init];
    for (TestCase *testCase in report.testCases) {
        if (testCase.result == TestResultFailed || testCase.result == TestResultError) {
            [failedTests addObject:@{
                @"testId": testCase.testId,
                @"name": testCase.name,
                @"result": @(testCase.result),
                @"errorMessage": testCase.errorMessage
            }];
        }
    }
    analysis[@"failedTests"] = failedTests;

    // 建议
    NSMutableArray *recommendations = [[NSMutableArray alloc] init];

    if (report.successRate < 0.8) {
        [recommendations addObject:@"Success rate is below 80%. Consider reviewing failed tests."];
    }

    if (averageTime > 1.0) {
        [recommendations addObject:@"Average test execution time is high. Consider optimizing test performance."];
    }

    if (report.errorTests > 0) {
        [recommendations addObject:@"Some tests encountered errors. Check test environment and dependencies."];
    }

    analysis[@"recommendations"] = recommendations;

    return [analysis copy];
}

- (NSArray<NSDictionary *> *)getFailedTestDetails:(TestReport *)report {
    if (!report) {
        return @[];
    }

    NSMutableArray *failedTests = [[NSMutableArray alloc] init];

    for (TestCase *testCase in report.testCases) {
        if (testCase.result == TestResultFailed || testCase.result == TestResultError) {
            [failedTests addObject:@{
                @"testId": testCase.testId,
                @"name": testCase.name,
                @"description": testCase.description,
                @"result": @(testCase.result),
                @"errorMessage": testCase.errorMessage,
                @"executionTime": @(testCase.executionTime),
                @"executedAt": testCase.executedAt ?: [NSNull null]
            }];
        }
    }

    return [failedTests copy];
}

- (NSDictionary *)getPerformanceStatistics:(TestReport *)report {
    if (!report || report.totalTests == 0) {
        return @{};
    }

    NSTimeInterval totalTime = report.totalExecutionTime;
    NSTimeInterval averageTime = totalTime / report.totalTests;
    NSTimeInterval maxTime = 0.0;
    NSTimeInterval minTime = INFINITY;

    NSMutableArray *slowTests = [[NSMutableArray alloc] init];
    NSMutableArray *fastTests = [[NSMutableArray alloc] init];

    for (TestCase *testCase in report.testCases) {
        NSTimeInterval time = testCase.executionTime;

        if (time > maxTime) {
            maxTime = time;
        }
        if (time < minTime) {
            minTime = time;
        }

        if (time > averageTime * 2) {
            [slowTests addObject:@{
                @"testId": testCase.testId,
                @"name": testCase.name,
                @"executionTime": @(time)
            }];
        } else if (time < averageTime * 0.5) {
            [fastTests addObject:@{
                @"testId": testCase.testId,
                @"name": testCase.name,
                @"executionTime": @(time)
            }];
        }
    }

    return @{
        @"totalExecutionTime": @(totalTime),
        @"averageExecutionTime": @(averageTime),
        @"maxExecutionTime": @(maxTime),
        @"minExecutionTime": @(minTime == INFINITY ? 0.0 : minTime),
        @"slowTests": slowTests,
        @"fastTests": fastTests,
        @"testsPerSecond": @(totalTime > 0 ? report.totalTests / totalTime : 0.0)
    };
}

#pragma mark - 工具方法

- (BOOL)validateTestEnvironment {
    // 检查必要的组件是否可用
    if (!_integrationTests) {
        NSLog(@"[TestRunner] Integration tests not initialized");
        return NO;
    }

    if (!_integrationTests.hotfixManager) {
        NSLog(@"[TestRunner] HotfixManager not available");
        return NO;
    }

    if (!_integrationTests.runtimeBridge) {
        NSLog(@"[TestRunner] RuntimeBridge not available");
        return NO;
    }

    if (!_integrationTests.assemblyLoader) {
        NSLog(@"[TestRunner] AssemblyLoader not available");
        return NO;
    }

    if (!_integrationTests.codeValidator) {
        NSLog(@"[TestRunner] CodeValidator not available");
        return NO;
    }

    if (_configuration.verboseLogging) {
        NSLog(@"[TestRunner] Test environment validation passed");
    }

    return YES;
}

- (void)cleanupTestEnvironment {
    // 重置统计信息
    [_integrationTests resetTestStatistics];

    // 清理可能的测试残留
    // 这里可以添加更多清理逻辑

    if (_configuration.verboseLogging) {
        NSLog(@"[TestRunner] Test environment cleaned up");
    }
}

- (NSArray<NSString *> *)getAvailableTests {
    return @[
        @"hotfix_manager_basics",
        @"method_replacement",
        @"method_restoration",
        @"method_registry",
        @"method_hook",
        @"parameter_mapper",
        @"runtime_bridge",
        @"assembly_loader",
        @"code_validator",
        @"complete_hotfix_flow",
        @"multiple_method_replacement",
        @"nested_method_calls",
        @"exception_handling",
        @"method_replacement_performance",
        @"memory_usage",
        @"concurrency_safety",
        @"long_running_stability",
        @"high_volume_stability",
        @"error_recovery"
    ];
}

- (nullable NSString *)getTestDescription:(NSString *)testId {
    NSDictionary *descriptions = @{
        @"hotfix_manager_basics": @"Test basic HotfixManager functionality",
        @"method_replacement": @"Test method replacement functionality",
        @"method_restoration": @"Test method restoration functionality",
        @"method_registry": @"Test MethodRegistry functionality",
        @"method_hook": @"Test MethodHook functionality",
        @"parameter_mapper": @"Test ParameterMapper functionality",
        @"runtime_bridge": @"Test RuntimeBridge functionality",
        @"assembly_loader": @"Test AssemblyLoader functionality",
        @"code_validator": @"Test CodeValidator functionality",
        @"complete_hotfix_flow": @"Test complete end-to-end hotfix flow",
        @"multiple_method_replacement": @"Test replacing multiple methods simultaneously",
        @"nested_method_calls": @"Test nested method calls with hotfix",
        @"exception_handling": @"Test exception handling in hotfix framework",
        @"method_replacement_performance": @"Test method replacement performance",
        @"memory_usage": @"Test memory usage during hotfix operations",
        @"concurrency_safety": @"Test thread safety of hotfix operations",
        @"long_running_stability": @"Test long-term stability",
        @"high_volume_stability": @"Test stability under high volume operations",
        @"error_recovery": @"Test error recovery capabilities"
    };

    return descriptions[testId];
}

@end
