//
//  RealWorldTest.h
//  ThreadInterpreterHotfixDemo
//
//  Created by AI Assistant on 2025-07-31.
//  Copyright © 2025 Demo. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark - 测试场景类

/**
 * 计算器类 - 模拟有bug的计算方法
 */
@interface BuggyCalculator : NSObject
- (NSInteger)divide:(NSInteger)a by:(NSInteger)b;  // 有除零bug
- (NSInteger)factorial:(NSInteger)n;               // 有负数bug
- (double)sqrt:(double)x;                          // 有负数bug
@end

/**
 * 网络请求类 - 模拟网络错误处理
 */
@interface NetworkManager : NSObject
- (NSString *)processResponse:(NSString *)response; // 有空值bug
- (BOOL)validateURL:(NSString *)url;               // 有验证bug
@end

/**
 * 数据处理类 - 模拟数据处理错误
 */
@interface DataProcessor : NSObject
- (NSArray *)parseJSONString:(NSString *)jsonString; // 有解析bug
- (NSString *)formatDate:(NSDate *)date;             // 有格式bug
@end

#pragma mark - 测试执行器

/**
 * 真实世界测试执行器 - 模拟实际应用中的热修复场景
 */
@interface RealWorldTest : NSObject

/**
 * 运行所有真实世界测试
 * @return 测试结果报告
 */
+ (NSDictionary *)runAllRealWorldTests;

/**
 * 测试场景1：修复除零错误
 * @return 测试结果
 */
+ (BOOL)testDivisionByZeroFix;

/**
 * 测试场景2：修复阶乘负数错误
 * @return 测试结果
 */
+ (BOOL)testFactorialNegativeFix;

/**
 * 测试场景3：修复平方根负数错误
 * @return 测试结果
 */
+ (BOOL)testSqrtNegativeFix;

/**
 * 测试场景4：修复网络响应空值错误
 * @return 测试结果
 */
+ (BOOL)testNetworkResponseNullFix;

/**
 * 测试场景5：修复URL验证错误
 * @return 测试结果
 */
+ (BOOL)testURLValidationFix;

/**
 * 测试场景6：修复JSON解析错误
 * @return 测试结果
 */
+ (BOOL)testJSONParsingFix;

/**
 * 测试场景7：修复日期格式化错误
 * @return 测试结果
 */
+ (BOOL)testDateFormattingFix;

/**
 * 性能对比测试
 * @param selector 方法选择器
 * @param targetClass 目标类
 * @param testData 测试数据
 * @return 性能对比结果
 */
+ (NSDictionary *)performanceComparisonForMethod:(SEL)selector
                                         inClass:(Class)targetClass
                                        testData:(NSArray *)testData;

/**
 * 稳定性测试 - 长时间运行
 * @param duration 测试持续时间（秒）
 * @return 稳定性测试结果
 */
+ (NSDictionary *)stabilityTestWithDuration:(NSTimeInterval)duration;

/**
 * 并发安全测试
 * @param threadCount 并发线程数
 * @param operationCount 每线程操作数
 * @return 并发测试结果
 */
+ (NSDictionary *)concurrencyTestWithThreads:(NSInteger)threadCount
                              operationCount:(NSInteger)operationCount;

@end

NS_ASSUME_NONNULL_END
