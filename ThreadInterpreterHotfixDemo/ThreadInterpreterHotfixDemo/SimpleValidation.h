//
//  SimpleValidation.h
//  ThreadInterpreterHotfixDemo
//
//  Created by AI Assistant on 2025-07-31.
//  Copyright © 2025 Demo. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 简化验证测试 - 快速验证核心功能
 */
@interface SimpleValidation : NSObject

/**
 * 运行简化验证测试
 * @return 验证结果字典
 */
+ (NSDictionary *)runSimpleValidation;

/**
 * 验证热修复管理器初始化
 */
+ (BOOL)validateHotfixManagerInitialization;

/**
 * 验证基础API调用
 */
+ (BOOL)validateBasicAPICall;

/**
 * 验证内存管理
 */
+ (BOOL)validateMemoryManagement;

/**
 * 打印验证结果
 */
+ (void)printValidationResults:(NSDictionary *)results;

@end

NS_ASSUME_NONNULL_END
